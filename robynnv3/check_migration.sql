-- Check if database migration was applied
-- Run this in your Supabase SQL Editor to verify the migration

-- 1. Check if new columns exist in content_documents
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'content_documents' 
AND column_name IN ('markdown_content', 'content_mode', 'section_count')
ORDER BY column_name;

-- 2. Check if content_sections table exists
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name = 'content_sections';

-- 3. Check if the functions exist
SELECT routine_name, routine_type 
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name IN ('regenerate_markdown_content', 'update_document_markdown_and_count');

-- 4. Sample existing documents to see current structure
SELECT id, title, content_type, content_mode, section_count, 
       CASE WHEN markdown_content IS NOT NULL THEN 'HAS_MARKDOWN' ELSE 'NO_MARKDOWN' END as markdown_status
FROM content_documents 
LIMIT 5;