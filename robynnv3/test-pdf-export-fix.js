#!/usr/bin/env node

/**
 * Test script to verify PDF export fixes
 * This script checks that the PDF export functionality has been properly fixed
 */

import { readFileSync, existsSync } from 'fs'
import { join } from 'path'

const ROBYNNV3_DIR = '/Users/<USER>/Dropbox/madhukar/robynnv3/code/robynnv3-main/robynnv3'

console.log('🔍 Testing PDF Export Fixes...\n')

// Check 1: Verify html2canvas is removed from pdf-generator.ts
console.log('1. Checking pdf-generator.ts import removal...')
const pdfGeneratorPath = join(ROBYNNV3_DIR, 'src/lib/utils/pdf-generator.ts')
if (existsSync(pdfGeneratorPath)) {
  const pdfGeneratorContent = readFileSync(pdfGeneratorPath, 'utf-8')
  if (pdfGeneratorContent.includes('import html2canvas')) {
    console.log('❌ html2canvas import still present in pdf-generator.ts')
  } else {
    console.log('✅ html2canvas import successfully removed from pdf-generator.ts')
  }
} else {
  console.log('❌ pdf-generator.ts not found')
}

// Check 2: Verify html2canvas is removed from package.json
console.log('\n2. Checking package.json dependency removal...')
const packageJsonPath = join(ROBYNNV3_DIR, 'package.json')
if (existsSync(packageJsonPath)) {
  const packageJsonContent = readFileSync(packageJsonPath, 'utf-8')
  const packageJson = JSON.parse(packageJsonContent)
  if (packageJson.dependencies && packageJson.dependencies['html2canvas']) {
    console.log('❌ html2canvas dependency still present in package.json')
  } else {
    console.log('✅ html2canvas dependency successfully removed from package.json')
  }
} else {
  console.log('❌ package.json not found')
}

// Check 3: Verify onclone hook is wired up
console.log('\n3. Checking onclone hook implementation...')
if (existsSync(pdfGeneratorPath)) {
  const pdfGeneratorContent = readFileSync(pdfGeneratorPath, 'utf-8')
  if (pdfGeneratorContent.includes('onclone: (clonedDoc) => {')) {
    console.log('✅ onclone hook successfully implemented')
    
    // Check for cleanup functions being called
    if (pdfGeneratorContent.includes('this.ensureStylesInClone(clonedDoc)') && 
        pdfGeneratorContent.includes('this.fixClonedDocumentColors(clonedDoc)')) {
      console.log('✅ CSS cleanup functions properly wired to onclone hook')
    } else {
      console.log('❌ CSS cleanup functions not properly wired to onclone hook')
    }
  } else {
    console.log('❌ onclone hook not implemented')
  }
}

// Check 4: Verify export buttons have type="button"
console.log('\n4. Checking export button type attributes...')
const seoCanvasPath = join(ROBYNNV3_DIR, 'src/lib/components/agui/SEOAGUICanvas.svelte')
if (existsSync(seoCanvasPath)) {
  const seoCanvasContent = readFileSync(seoCanvasPath, 'utf-8')
  
  // Check for PDF export button
  const pdfButtonMatch = seoCanvasContent.match(/<button[^>]*onclick=\{handleExport\}[^>]*>/g)
  if (pdfButtonMatch && pdfButtonMatch[0].includes('type="button"')) {
    console.log('✅ PDF export button has type="button" attribute')
  } else {
    console.log('❌ PDF export button missing type="button" attribute')
  }
  
  // Check for Markdown export button
  const mdButtonMatch = seoCanvasContent.match(/<button[^>]*onclick=\{handleMarkdownExport\}[^>]*>/g)
  if (mdButtonMatch && mdButtonMatch[0].includes('type="button"')) {
    console.log('✅ Markdown export button has type="button" attribute')
  } else {
    console.log('❌ Markdown export button missing type="button" attribute')
  }
} else {
  console.log('❌ SEOAGUICanvas.svelte not found')
}

// Check 5: Verify CSS cleanup functions exist
console.log('\n5. Checking CSS cleanup function availability...')
if (existsSync(pdfGeneratorPath)) {
  const pdfGeneratorContent = readFileSync(pdfGeneratorPath, 'utf-8')
  
  if (pdfGeneratorContent.includes('ensureStylesInClone(clonedDoc: Document)')) {
    console.log('✅ ensureStylesInClone function exists')
  } else {
    console.log('❌ ensureStylesInClone function missing')
  }
  
  if (pdfGeneratorContent.includes('fixClonedDocumentColors(clonedDoc: Document)')) {
    console.log('✅ fixClonedDocumentColors function exists')
  } else {
    console.log('❌ fixClonedDocumentColors function missing')
  }
  
  // Check for oklch cleanup
  if (pdfGeneratorContent.includes('oklch') && pdfGeneratorContent.includes('var(')) {
    console.log('✅ CSS cleanup functions handle oklch and CSS variables')
  } else {
    console.log('❌ CSS cleanup functions may not handle modern CSS properly')
  }
}

console.log('\n📊 PDF Export Fix Summary:')
console.log('The following changes were implemented to fix the PDF export issues:')
console.log('• Removed html2canvas v1.4.1 dependency (let jsPDF use its internal v1.6+ copy)')
console.log('• Added type="button" to export buttons to prevent form submission')
console.log('• Wired CSS cleanup functions to onclone hook to sanitize Tailwind/DaisyUI styles')
console.log('• Enhanced color cleanup to handle oklch() and CSS variable issues')
console.log('')
console.log('🎯 Expected Result: PDF export should now work without page refresh or oklch errors')
