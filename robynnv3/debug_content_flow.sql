-- DEBUG SCRIPT: Step-by-step analysis of content save flow
-- Run this in your Supabase SQL Editor to diagnose the issue

-- 1. Check if content_sections table exists and structure
SELECT 
    'content_sections table exists: ' || 
    EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'content_sections')::text as table_check;

-- 2. Check columns in content_sections table
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'content_sections' 
ORDER BY ordinal_position;

-- 3. Check if regenerate_markdown_content function exists
SELECT 
    'regenerate_markdown_content function exists: ' || 
    EXISTS (
        SELECT FROM information_schema.routines 
        WHERE routine_name = 'regenerate_markdown_content' 
        AND routine_type = 'FUNCTION'
    )::text as function_check;

-- 4. Check RLS policies on content_sections
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE tablename = 'content_sections';

-- 5. Check if there are any existing content_sections records
SELECT COUNT(*) as total_sections, 
       COUNT(DISTINCT document_id) as unique_documents,
       COUNT(DISTINCT user_id) as unique_users
FROM content_sections;

-- 6. Check content_documents with accumulation mode
SELECT id, title, content_mode, section_count, 
       CASE WHEN markdown_content IS NULL THEN 'NULL' 
            WHEN markdown_content = '' THEN 'EMPTY' 
            ELSE 'HAS_CONTENT' 
       END as markdown_status,
       created_at
FROM content_documents 
WHERE content_mode = 'accumulation' 
ORDER BY created_at DESC 
LIMIT 5;

-- 7. Check for recent Research Session documents
SELECT id, title, content_mode, section_count, conversation_count,
       LENGTH(COALESCE(markdown_content, '')) as markdown_length,
       created_at, updated_at
FROM content_documents 
WHERE title LIKE '%Research Session%' 
ORDER BY created_at DESC 
LIMIT 3;

-- 8. If you have a specific document ID, replace 'YOUR_DOCUMENT_ID' and run:
-- SELECT cs.*, cd.title 
-- FROM content_sections cs
-- JOIN content_documents cd ON cs.document_id = cd.id
-- WHERE cs.document_id = 'YOUR_DOCUMENT_ID'
-- ORDER BY cs.order_index;

-- 9. Test the regenerate_markdown_content function with a real document ID
-- (Replace 'YOUR_DOCUMENT_ID' with an actual document ID from step 7)
-- SELECT regenerate_markdown_content('YOUR_DOCUMENT_ID');