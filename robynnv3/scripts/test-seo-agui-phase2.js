#!/usr/bin/env node

/**
 * Test script for SEO AG-UI Phase 2 Components
 * This script validates that Phase 2 components integrate correctly with Phase 1 infrastructure
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

const TESTS = {
  passed: 0,
  failed: 0,
  results: []
};

function logTest(name, passed, details = '') {
  const status = passed ? '✅ PASS' : '❌ FAIL';
  console.log(`${status}: ${name}`);
  if (details) console.log(`   ${details}`);
  
  TESTS.results.push({ name, passed, details });
  if (passed) TESTS.passed++;
  else TESTS.failed++;
}

function fileExists(filePath) {
  return fs.existsSync(filePath);
}

function checkFileContent(filePath, searchText) {
  if (!fileExists(filePath)) return false;
  const content = fs.readFileSync(filePath, 'utf8');
  return content.includes(searchText);
}

function checkMultipleContent(filePath, searchTexts) {
  if (!fileExists(filePath)) return false;
  const content = fs.readFileSync(filePath, 'utf8');
  return searchTexts.every(text => content.includes(text));
}

console.log('🔍 Testing SEO AG-UI Phase 2 Components...\n');

// Test 1: Verify Phase 1 infrastructure still works
console.log('🏗️  Phase 1 Infrastructure Regression Tests:');
const phase1Files = [
  'src/lib/agui/seo-conversation-store.ts',
  'src/lib/agui/seo-canvas-store.ts',
  'src/lib/services/seo-agui-bridge.ts',
  'src/routes/(admin)/dashboard/[envSlug]/agent-seo/+server.ts'
];

phase1Files.forEach(file => {
  const exists = fileExists(file);
  logTest(`Phase 1 file intact: ${file}`, exists);
});

// Test 2: Check Phase 2 component files exist
console.log('\n📱 Phase 2 Component Tests:');
const phase2Files = [
  'src/lib/components/agui/SEOAGUICanvas.svelte',
  'src/lib/components/agui/SEOAGUISidebar.svelte'
];

phase2Files.forEach(file => {
  const exists = fileExists(file);
  logTest(`Phase 2 component exists: ${file}`, exists);
});

// Test 3: Check SEO Canvas component implementation
console.log('\n🎨 SEO Canvas Component Tests:');
if (fileExists('src/lib/components/agui/SEOAGUICanvas.svelte')) {
  const requiredCanvasContent = [
    'seoCanvasStore',
    'SEOCanvasItem',
    'seoConversationStore',
    'keyword-research',
    'gap-analysis'
  ];
  
  const canvasValid = checkMultipleContent('src/lib/components/agui/SEOAGUICanvas.svelte', requiredCanvasContent);
  logTest('SEO Canvas implements required features', canvasValid);
  
  const hasExports = checkFileContent('src/lib/components/agui/SEOAGUICanvas.svelte', 'export');
  logTest('SEO Canvas has proper exports', hasExports);
}

// Test 4: Check SEO Sidebar component implementation
console.log('\n📊 SEO Sidebar Component Tests:');
if (fileExists('src/lib/components/agui/SEOAGUISidebar.svelte')) {
  const requiredSidebarContent = [
    'seoConversationStore',
    'seoProgressSteps',
    'sendSEOMessage',
    'ProgressStep'
  ];
  
  const sidebarValid = checkMultipleContent('src/lib/components/agui/SEOAGUISidebar.svelte', requiredSidebarContent);
  logTest('SEO Sidebar implements required features', sidebarValid);
  
  const hasMessageInput = checkFileContent('src/lib/components/agui/SEOAGUISidebar.svelte', 'input');
  logTest('SEO Sidebar has message input', hasMessageInput);
}

// Test 5: Check SEO page integration
console.log('\n🔗 Page Integration Tests:');
const seoPagePath = 'src/routes/(admin)/dashboard/[envSlug]/agent-seo/+page.svelte';
if (fileExists(seoPagePath)) {
  const hasAguiImports = checkMultipleContent(seoPagePath, [
    'SEOAGUICanvas',
    'SEOAGUISidebar'
  ]);
  logTest('SEO page imports AG-UI components', hasAguiImports);
  
  const hasFeatureFlags = checkMultipleContent(seoPagePath, [
    'AGUI_ENABLED',
    'AGUI_STREAMING_ENABLED'
  ]);
  logTest('SEO page uses feature flags', hasFeatureFlags);
  
  const hasConditionalRendering = checkFileContent(seoPagePath, '{#if');
  logTest('SEO page has conditional rendering', hasConditionalRendering);
}

// Test 6: Check component exports
console.log('\n📦 Component Export Tests:');
const componentIndexPath = 'src/lib/components/agui/index.ts';
if (fileExists(componentIndexPath)) {
  const exportsComponents = checkMultipleContent(componentIndexPath, [
    'SEOAGUICanvas',
    'SEOAGUISidebar'
  ]);
  logTest('Component index exports SEO components', exportsComponents);
} else {
  // Check if components are exported individually
  const canvasExported = fileExists('src/lib/components/agui/SEOAGUICanvas.svelte');
  const sidebarExported = fileExists('src/lib/components/agui/SEOAGUISidebar.svelte');
  logTest('SEO components are exportable', canvasExported && sidebarExported);
}

// Test 7: Check TypeScript compilation
console.log('\n🔧 TypeScript Integration Tests:');
try {
  // Test if the project can be type-checked
  execSync('pnpm run check --reporter=silent', { stdio: 'pipe', timeout: 30000 });
  logTest('TypeScript compilation passes', true);
} catch (error) {
  // Check if it's just warnings vs actual errors
  const errorOutput = error.stdout?.toString() || error.stderr?.toString() || '';
  const hasErrors = errorOutput.includes('Error:');
  const onlyWarnings = !hasErrors && errorOutput.includes('Warning:');
  
  if (onlyWarnings) {
    logTest('TypeScript compilation passes (warnings only)', true, 'Has warnings but no errors');
  } else {
    logTest('TypeScript compilation passes', false, 'Has compilation errors');
  }
}

// Test 8: Check build system integration
console.log('\n🏗️  Build System Tests:');
try {
  // Test if the project can be built
  execSync('pnpm run build', { stdio: 'pipe', timeout: 60000 });
  logTest('Build system passes', true);
} catch (error) {
  logTest('Build system passes', false, 'Build failed');
}

// Test 9: Check store integration
console.log('\n🔄 Store Integration Tests:');
const bridgeHasNewComponents = checkMultipleContent('src/lib/services/seo-agui-bridge.ts', [
  'SEOAGUICanvas',
  'SEOAGUISidebar'
].map(comp => comp.toLowerCase()));

if (!bridgeHasNewComponents) {
  // It's okay if bridge doesn't reference components directly
  logTest('Bridge service compatible with new components', true, 'Bridge uses store-based integration');
} else {
  logTest('Bridge service references new components', true);
}

// Test 10: Check feature flag usage
console.log('\n🚩 Feature Flag Tests:');
const envConfigValid = checkMultipleContent('src/lib/agents/shared/env.ts', [
  'AGUI_ENABLED',
  'AGUI_STREAMING_ENABLED'
]);
logTest('Environment config has required flags', envConfigValid);

// Test 11: Check memory integration
console.log('\n💾 Memory Integration Tests:');
const memoryIntegrationValid = checkMultipleContent('src/routes/(admin)/dashboard/[envSlug]/agent-seo/+server.ts', [
  'Memory',
  'PostgresStore',
  'performSEOAGUIStreaming'
]);
logTest('Memory integration works with AG-UI', memoryIntegrationValid);

// Test 12: Check backward compatibility
console.log('\n🔄 Backward Compatibility Tests:');
const legacyStoreExists = fileExists('src/lib/stores/seo-agent-store.ts');
logTest('Legacy SEO store still exists', legacyStoreExists);

if (legacyStoreExists) {
  const bridgeIntegratesLegacy = checkFileContent('src/lib/services/seo-agui-bridge.ts', 'seo-agent-store');
  logTest('Bridge integrates with legacy store', bridgeIntegratesLegacy);
}

// Summary
console.log('\n📊 Test Summary:');
console.log(`✅ Passed: ${TESTS.passed}`);
console.log(`❌ Failed: ${TESTS.failed}`);
console.log(`📊 Total: ${TESTS.passed + TESTS.failed}`);

if (TESTS.failed > 0) {
  console.log('\n❗ Failed tests need attention before Phase 2 completion');
  console.log('\nFailed Tests:');
  TESTS.results.filter(r => !r.passed).forEach(r => {
    console.log(`  ❌ ${r.name}${r.details ? ` - ${r.details}` : ''}`);
  });
  process.exit(1);
} else {
  console.log('\n🎉 All Phase 2 tests passed! SEO AG-UI integration complete.');
  process.exit(0);
}
