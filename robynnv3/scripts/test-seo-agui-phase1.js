#!/usr/bin/env node

/**
 * Test script for SEO AG-UI Phase 1 Infrastructure
 * This script validates that all Phase 1 components are working correctly
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

const TESTS = {
  passed: 0,
  failed: 0,
  results: []
};

function logTest(name, passed, details = '') {
  const status = passed ? '✅ PASS' : '❌ FAIL';
  console.log(`${status}: ${name}`);
  if (details) console.log(`   ${details}`);
  
  TESTS.results.push({ name, passed, details });
  if (passed) TESTS.passed++;
  else TESTS.failed++;
}

function fileExists(filePath) {
  return fs.existsSync(filePath);
}

function checkFileContent(filePath, searchText) {
  if (!fileExists(filePath)) return false;
  const content = fs.readFileSync(filePath, 'utf8');
  return content.includes(searchText);
}

console.log('🔍 Testing SEO AG-UI Phase 1 Infrastructure...\n');

// Test 1: Check if all required files exist
console.log('📁 File Structure Tests:');
const requiredFiles = [
  'src/lib/agui/seo-conversation-store.ts',
  'src/lib/agui/seo-canvas-store.ts',
  'src/lib/services/seo-agui-bridge.ts',
  'src/routes/(admin)/dashboard/[envSlug]/agent-seo/+server.ts',
  'src/lib/agents/seo-strategist.ts',
  'supabase/migrations/20250201120000_add_mastra_memory_tables.sql'
];

requiredFiles.forEach(file => {
  const exists = fileExists(file);
  logTest(`File exists: ${file}`, exists);
});

// Test 2: Check environment configuration
console.log('\n⚙️  Environment Configuration Tests:');
const envConfigExists = fileExists('src/lib/agents/shared/env.ts');
logTest('Environment config file exists', envConfigExists);

if (envConfigExists) {
  const hasAguiFlags = checkFileContent('src/lib/agents/shared/env.ts', 'AGUI_ENABLED') && 
                       checkFileContent('src/lib/agents/shared/env.ts', 'AGUI_STREAMING_ENABLED');
  logTest('AG-UI feature flags defined', hasAguiFlags);
}

// Test 3: Check store implementations
console.log('\n🏪 Store Implementation Tests:');
const conversationStoreValid = checkFileContent('src/lib/agui/seo-conversation-store.ts', 'seoConversationStore');
logTest('SEO conversation store properly implemented', conversationStoreValid);

const canvasStoreValid = checkFileContent('src/lib/agui/seo-canvas-store.ts', 'seoCanvasStore');
logTest('SEO canvas store properly implemented', canvasStoreValid);

// Test 4: Check bridge service
console.log('\n🌉 Bridge Service Tests:');
const bridgeValid = checkFileContent('src/lib/services/seo-agui-bridge.ts', 'SEOAGUIBridge');
logTest('SEO AG-UI bridge service implemented', bridgeValid);

// Test 5: Check server handler
console.log('\n🖥️  Server Handler Tests:');
const serverHasAguiSupport = checkFileContent('src/routes/(admin)/dashboard/[envSlug]/agent-seo/+server.ts', 'performSEOAGUIStreaming');
logTest('Server supports AG-UI streaming', serverHasAguiSupport);

const serverHasMemory = checkFileContent('src/routes/(admin)/dashboard/[envSlug]/agent-seo/+server.ts', 'Memory');
logTest('Server has memory integration', serverHasMemory);

// Test 6: Check agent configuration
console.log('\n🤖 Agent Configuration Tests:');
const agentHasMemory = checkFileContent('src/lib/agents/seo-strategist.ts', '@mastra/memory') &&
                       checkFileContent('src/lib/agents/seo-strategist.ts', '@mastra/pg');
logTest('SEO agent has memory dependencies', agentHasMemory);

const agentHasMemoryConfig = checkFileContent('src/lib/agents/seo-strategist.ts', 'PostgresStore');
logTest('SEO agent configured with PostgreSQL memory', agentHasMemoryConfig);

// Test 7: Check package dependencies
console.log('\n📦 Package Dependencies Tests:');
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  const hasMastraMemory = packageJson.dependencies['@mastra/memory'] || packageJson.devDependencies['@mastra/memory'];
  const hasMastraPg = packageJson.dependencies['@mastra/pg'] || packageJson.devDependencies['@mastra/pg'];
  
  logTest('Mastra memory package installed', !!hasMastraMemory);
  logTest('Mastra PostgreSQL package installed', !!hasMastraPg);
} catch (e) {
  logTest('Package.json readable', false, e.message);
}

// Test 8: Database migration check
console.log('\n🗄️  Database Migration Tests:');
const migrationValid = checkFileContent('supabase/migrations/20250201120000_add_mastra_memory_tables.sql', 'mastra_conversations');
logTest('Memory tables migration exists', migrationValid);

// Summary
console.log('\n📊 Test Summary:');
console.log(`✅ Passed: ${TESTS.passed}`);
console.log(`❌ Failed: ${TESTS.failed}`);
console.log(`📊 Total: ${TESTS.passed + TESTS.failed}`);

if (TESTS.failed > 0) {
  console.log('\n❗ Failed tests need attention before proceeding to Phase 2');
  process.exit(1);
} else {
  console.log('\n🎉 All Phase 1 infrastructure tests passed! Ready for Phase 2.');
  process.exit(0);
}
