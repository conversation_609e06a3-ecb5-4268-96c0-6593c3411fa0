# SEO AG-UI Phase 1 Testing Guide

This guide provides step-by-step instructions to test the Phase 1 infrastructure implementation.

## Prerequisites

1. **Environment Variables**: Ensure these are set in your `.env` file:
   ```
   AGUI_ENABLED=true
   AGUI_STREAMING_ENABLED=true
   ```

2. **Database**: Ensure Supabase is running and connected

3. **Dependencies**: Ensure all packages are installed:
   ```bash
   cd robynnv3
   pnpm install
   ```

## Automated Testing

Run the automated test script:

```bash
cd robynnv3
node scripts/test-seo-agui-phase1.js
```

## Manual Testing Steps

### 1. Environment Configuration Test

**Test**: Verify environment variables are accessible
```bash
cd robynnv3
pnpm run dev
```

**Expected**: No errors about missing AG-UI environment variables

### 2. Database Migration Test

**Test**: Apply the memory tables migration
```bash
cd robynnv3
npx supabase db reset --linked
```

**Expected**: Migration applies successfully without errors

**Verify**: Check that memory tables exist in your Supabase dashboard:
- `mastra_memory_entities`
- `mastra_memory_relations` 
- `mastra_memory_observations`

### 3. Store Initialization Test

**Test**: Import and initialize stores
```javascript
// In browser console or test file
import { seoConversationStore } from '$lib/agui/seo-conversation-store.js';
import { seoCanvasStore } from '$lib/agui/seo-canvas-store.js';

console.log('Conversation store:', seoConversationStore);
console.log('Canvas store:', seoCanvasStore);
```

**Expected**: Both stores initialize without errors

### 4. Bridge Service Test

**Test**: Verify bridge service can be imported
```javascript
import { SEOAGUIBridge } from '$lib/services/seo-agui-bridge.js';
const bridge = new SEOAGUIBridge();
console.log('Bridge initialized:', bridge);
```

**Expected**: Bridge initializes without errors

### 5. Server Endpoint Test - Legacy Streaming

**Test**: Make a request to the SEO endpoint without AG-UI
```bash
curl -X POST http://localhost:5173/dashboard/test-env/agent-seo \
  -H "Content-Type: application/json" \
  -d '{"message": "test seo analysis", "userId": "test-user"}'
```

**Expected**: 
- Response uses legacy streaming format
- No errors in server logs
- Response includes SEO analysis

### 6. Server Endpoint Test - AG-UI Streaming

**Test**: Make a request with AG-UI streaming enabled
```bash
curl -X POST "http://localhost:5173/dashboard/test-env/agent-seo?stream=true" \
  -H "Content-Type: application/json" \
  -d '{"message": "test seo analysis", "userId": "test-user"}'
```

**Expected**:
- Response uses AG-UI streaming format
- Bridge service is called
- Conversation is stored in memory

### 7. Memory Persistence Test

**Test**: Send multiple messages and verify conversation history
```bash
# First message
curl -X POST "http://localhost:5173/dashboard/test-env/agent-seo?stream=true" \
  -H "Content-Type: application/json" \
  -d '{"message": "analyze my website seo", "userId": "test-user-123"}'

# Second message (should have context from first)
curl -X POST "http://localhost:5173/dashboard/test-env/agent-seo?stream=true" \
  -H "Content-Type: application/json" \
  -d '{"message": "what did you recommend before?", "userId": "test-user-123"}'
```

**Expected**:
- Second response references previous conversation
- Memory entities created in database
- Conversation thread maintained

### 8. Error Handling Test

**Test**: Send invalid requests
```bash
# Missing userId
curl -X POST "http://localhost:5173/dashboard/test-env/agent-seo?stream=true" \
  -H "Content-Type: application/json" \
  -d '{"message": "test"}'

# Invalid JSON
curl -X POST "http://localhost:5173/dashboard/test-env/agent-seo?stream=true" \
  -H "Content-Type: application/json" \
  -d 'invalid json'
```

**Expected**:
- Appropriate error responses
- No server crashes
- Errors logged properly

## Database Verification

After running tests, check your Supabase database:

1. **Memory Tables**: Verify tables exist and have correct schema
2. **Memory Data**: Check that conversation data is being stored
3. **Resource Isolation**: Verify `resource_id` follows pattern `seo_user_{userId}`

## Troubleshooting

### Common Issues:

1. **Environment Variables Not Found**
   - Ensure `.env` file is in `robynnv3/` directory
   - Restart dev server after adding variables

2. **Migration Errors**
   - Check Supabase connection
   - Verify migration SQL syntax
   - Check for conflicting table names

3. **Import Errors**
   - Run `pnpm run check` to verify TypeScript
   - Check for missing dependencies
   - Verify file paths are correct

4. **Memory Not Working**
   - Check database connection string
   - Verify Supabase RLS policies
   - Check agent initialization

## Success Criteria

✅ All automated tests pass
✅ Environment variables are accessible
✅ Database migration applies successfully
✅ Stores initialize without errors
✅ Bridge service works correctly
✅ Both streaming modes work
✅ Memory persistence functions
✅ Error handling is robust

When all criteria are met, Phase 1 is complete and ready for Phase 2.
