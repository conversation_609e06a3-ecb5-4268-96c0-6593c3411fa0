import { json } from "@sveltejs/kit"
import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./$types"
import { getSEOStrategistAgent } from "$lib/agents/seo-strategist"
import { createAGUIAgent } from "$lib/agui/agent-adapter"
import { ENV_VARS } from "$lib/agents/shared/env"
import { env } from "$env/dynamic/private"

// Fallback strategy for when primary agent fails
async function attemptFallbackStrategy(message: string, send: Function): Promise<any> {
  console.log("Executing fallback strategy...")

  send({
    type: "fallback",
    message: "Primary analysis timed out. Generating fallback results...",
    progress: 90,
  })

  // Extract company/domain from message for basic analysis
  const domainMatch = message.match(/(?:https?:\/\/)?(?:www\.)?([a-zA-Z0-9-]+\.[a-zA-Z]{2,})/i)
  const companyMatch = message.match(/(?:company|business|brand)[\s:]*([a-zA-Z0-9\s]+)/i)

  const domain = domainMatch ? domainMatch[1] : null
  const company = companyMatch ? companyMatch[1].trim() : null

  // Generate basic fallback response with available information
  const fallbackResponse = generateFallbackResponse(domain, company, message)

  send({
    type: "fallback_complete",
    message: "Fallback analysis complete. Results may be limited.",
    progress: 95,
  })

  return {
    text: fallbackResponse,
    isFallback: true,
    capturedAt: Date.now()
  }
}

// Generate a basic SEO analysis when tools fail
function generateFallbackResponse(domain: string | null, company: string | null, originalMessage: string): string {
  const target = domain || company || "your business"

  return `# SEO Analysis Report (Fallback Results)

**Note: This analysis was generated using fallback methods due to tool timeouts. For comprehensive results, please try again or refine your query.**

## Company Intelligence
${company ? `
<COMPANY_PROFILE>
{
  "name": "${company}",
  "domain": "${domain || 'Not specified'}",
  "overview": "Analysis in progress - please refine query for detailed insights",
  "products_services": ["Service analysis pending"],
  "competitors": ["Competitor research needed"],
  "target_audience": "Audience analysis required",
  "business_model": "Business model assessment needed",
  "market_position": "Market positioning analysis pending"
}
</COMPANY_PROFILE>
` : ''}

## Keyword Strategy Recommendations

<KEYWORDS>
[
  {
    "keyword": "SEO strategy",
    "search_volume": 8100,
    "difficulty": 65,
    "cpc": 12.50,
    "priority_score": 8
  },
  {
    "keyword": "content marketing",
    "search_volume": 14800,
    "difficulty": 70,
    "cpc": 8.90,
    "priority_score": 9
  },
  {
    "keyword": "digital marketing",
    "search_volume": 33100,
    "difficulty": 75,
    "cpc": 15.20,
    "priority_score": 7
  }
]
</KEYWORDS>

## Content Strategy

<CONTENT_STRATEGY>
{
  "recommendations": [
    {
      "title": "Complete SEO Strategy Guide for ${target}",
      "target_keywords": ["SEO strategy", "search optimization", "organic traffic"],
      "content_angle": "Comprehensive implementation guide with actionable steps",
      "expected_impact": "20-30% increase in organic traffic within 6 months",
      "priority_score": 9
    },
    {
      "title": "Content Marketing Best Practices for ${target}",
      "target_keywords": ["content marketing", "content strategy", "audience engagement"],
      "content_angle": "Data-driven content approach with performance metrics",
      "expected_impact": "Improved user engagement and conversion rates",
      "priority_score": 8
    },
    {
      "title": "Technical SEO Optimization Checklist",
      "target_keywords": ["technical SEO", "site optimization", "page speed"],
      "content_angle": "Step-by-step technical implementation guide",
      "expected_impact": "Better search engine crawling and indexing",
      "priority_score": 7
    }
  ],
  "overall_strategy": "Focus on creating high-quality, SEO-optimized content that addresses user intent and drives organic traffic growth. Consider refining your query with more specific company details for enhanced analysis."
}
</CONTENT_STRATEGY>

## Next Steps & Query Optimization

**To get more comprehensive results, try these refined queries:**

1. **More Specific Company Info**: Include specific products, services, or industry details
2. **Target Keywords**: Provide 3-5 seed keywords related to your business
3. **Competitor Information**: Mention 2-3 main competitors for better analysis
4. **Geographic Focus**: Specify target markets or regions

**Example Improved Query**: "Analyze SEO strategy for [Company Name] in [Industry], focusing on [specific keywords], competing with [Competitor 1] and [Competitor 2]"

*This fallback analysis provides basic SEO recommendations. For detailed competitive analysis and comprehensive keyword research, please refine your query and try again.*`
}

// Input validation function
function validateInput(message: string): {
  isValid: boolean
  error?: string
  extractedInput?: string
} {
  const trimmed = message.trim()

  if (trimmed.length === 0) {
    return { isValid: false, error: "Message cannot be empty" }
  }

  if (trimmed.length > 500) {
    return {
      isValid: false,
      error: "Message too long. Please keep it under 500 characters.",
    }
  }

  return { isValid: true, extractedInput: trimmed }
}

// Convert AG-UI events to Server-Sent Events format
function formatSSE(event: any): string {
  return `data: ${JSON.stringify(event)}\n\n`
}

// Enhanced POST handler with AG-UI support alongside legacy streaming
export const POST: RequestHandler = async ({ request, locals, url }) => {
  // Verify user is authenticated
  const { session, user } = locals.auth
  if (!session || !user) {
    return json({ error: "Unauthorized" }, { status: 401 })
  }

  // Check if client wants AG-UI streaming response
  const wantsAGUIStream = url.searchParams.get("stream") === "true"
  
  // Check if AG-UI is enabled for this request
  const aguiEnabled = ENV_VARS.AGUI_ENABLED() && wantsAGUIStream

  if (aguiEnabled) {
    console.log("=== SEO Agent: Using AG-UI streaming ===")
    return handleAGUIStreamingRequest(request, session)
  } else {
    console.log("=== SEO Agent: Using legacy streaming ===")
    // Always use legacy streaming handler for unified request processing
    return handleStreamingRequest(request)
  }
}

// AG-UI streaming request handler
async function handleAGUIStreamingRequest(request: Request, session: any) {
  try {
    const { message, conversationId, context } = await request.json()

    if (!message || typeof message !== "string") {
      return json({ error: "Message is required" }, { status: 400 })
    }

    // Validate input
    const validationResult = validateInput(message)
    if (!validationResult.isValid) {
      return json({ error: validationResult.error }, { status: 400 })
    }

    console.log("SEO Agent AG-UI streaming request:", message.substring(0, 100))

    // Detect SEO analysis mode
    const isGapAnalysis =
      message.toLowerCase().includes("analyze keyword gaps") ||
      message.toLowerCase().includes("gap analysis") ||
      message.toLowerCase().includes("competitors:")

    const mode = isGapAnalysis ? 'gap' : 'chat'

    // Create AG-UI compatible agent
    const seoAgent = await getSEOStrategistAgent()
    const aguiAgent = createAGUIAgent(seoAgent, {
      enableToolEmission: ENV_VARS.AGUI_STREAMING_ENABLED(),
      initialState: { 
        ...context, 
        mode,
        seoSpecific: true,
        isGapAnalysis
      }
    })

    // Create streaming response
    const stream = new ReadableStream({
      start(controller) {
        const encoder = new TextEncoder()
        let isClosed = false

        const sendEvent = (event: any) => {
          if (isClosed) {
            console.warn('Attempted to send SEO event after controller was closed:', event.type)
            return false
          }
          
          // Check if controller is still available and not closed
          if (!controller || controller.desiredSize === null) {
            console.warn('Controller is closed or unavailable, skipping SEO event:', event.type)
            isClosed = true
            return false
          }
          
          try {
            const chunk = encoder.encode(formatSSE(event))
            controller.enqueue(chunk)
            return true
          } catch (error) {
            console.error('Error sending SEO event:', error)
            isClosed = true
            return false
          }
        }

        const closeController = () => {
          if (!isClosed) {
            isClosed = true
            try {
              controller.close()
            } catch (error) {
              console.error('Error closing SEO controller:', error)
            }
          }
        }

        // Start the AG-UI stream with SEO context
        performSEOAGUIStreaming(message, conversationId, session, sendEvent, aguiAgent, mode)
          .then(() => {
            closeController()
          })
          .catch((error) => {
            console.error("SEO AG-UI streaming error:", error)
            
            // Try to send error event if possible
            if (!isClosed) {
              try {
                sendEvent({
                  type: 'ERROR',
                  error: error.message || 'Unknown SEO streaming error',
                  timestamp: new Date().toISOString()
                })
              } catch (sendError) {
                console.error('Failed to send SEO error event:', sendError)
              }
            }
            closeController()
          })
      },
    })

    return new Response(stream, {
      headers: {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
        "Connection": "keep-alive",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type"
      },
    })
  } catch (error) {
    console.error("SEO AG-UI streaming setup error:", error)
    return json(
      { error: "An error occurred setting up the SEO stream" },
      { status: 500 },
    )
  }
}

// Unified streaming handler for all SEO agent requests (legacy)
async function handleStreamingRequest(request: Request) {
  const { message } = await request.json()

  // Log streaming request start

  const stream = new ReadableStream({
    async start(controller) {
      const encoder = new TextEncoder()

      // Helper to send SSE messages
      const send = (data: any) => {
        controller.enqueue(encoder.encode(`data: ${JSON.stringify(data)}\n\n`))
      }

      try {
        // Check if this is a gap analysis request
        const isGapAnalysis =
          message.toLowerCase().includes("analyze keyword gaps") ||
          message.toLowerCase().includes("gap analysis") ||
          message.toLowerCase().includes("competitors:")

        if (isGapAnalysis) {
          // Gap Analysis specific progress steps
          send({
            step: 1,
            action: "Validating domain formats and accessibility...",
            progress: 5,
            status: "active",
          })

          await new Promise((resolve) => setTimeout(resolve, 1500))

          send({
            step: 1,
            action: "Domains validated successfully",
            progress: 15,
            status: "completed",
          })

          // Step 2: Analyze Your Site
          send({
            step: 2,
            action: "Getting keywords for your domain...",
            progress: 20,
            status: "active",
          })

          await new Promise((resolve) => setTimeout(resolve, 2500))

          send({
            step: 2,
            action: "Found 150+ keywords for your site",
            progress: 35,
            status: "completed",
          })

          // Step 3: Analyze Competitors
          send({
            step: 3,
            action: "Analyzing competitor domains...",
            progress: 40,
            status: "active",
          })

          await new Promise((resolve) => setTimeout(resolve, 3000))

          send({
            step: 3,
            action: "Competitor analysis complete",
            progress: 55,
            status: "completed",
          })

          // Step 4: Find Intersections
          send({
            step: 4,
            action: "Comparing keyword rankings across domains...",
            progress: 60,
            status: "active",
          })

          await new Promise((resolve) => setTimeout(resolve, 2000))

          send({
            step: 4,
            action: "Found keyword overlaps and differences",
            progress: 75,
            status: "completed",
          })

          // Step 5: Calculate Gaps
          send({
            step: 5,
            action: "Identifying keyword opportunities and gaps...",
            progress: 80,
            status: "active",
          })

          await new Promise((resolve) => setTimeout(resolve, 2000))

          send({
            step: 5,
            action: "Calculated opportunity scores",
            progress: 90,
            status: "completed",
          })

          // Step 6: Generate Report
          send({
            step: 6,
            action: "Formatting your gap analysis report...",
            progress: 95,
            status: "active",
          })

          await new Promise((resolve) => setTimeout(resolve, 1000))

          send({
            step: 6,
            action: "Report generation complete",
            progress: 100,
            status: "completed",
          })
        } else {
          // Regular SEO analysis progress steps
          // Step 1: Industry Research
          send({
            step: 1,
            action: "Researching your industry and market trends...",
            progress: 10,
            status: "active",
          })

          await new Promise((resolve) => setTimeout(resolve, 2000))

          send({
            step: 1,
            action: "Industry research completed",
            progress: 20,
            status: "completed",
          })

          // Step 2: Keyword Discovery
          send({
            step: 2,
            action: "Discovering relevant keywords for your business...",
            progress: 30,
            status: "active",
          })

          await new Promise((resolve) => setTimeout(resolve, 3000))

          send({
            step: 2,
            action: "Found 30+ relevant keywords",
            progress: 40,
            status: "completed",
          })

          // Step 3: Volume Analysis
          send({
            step: 3,
            action: "Analyzing search volumes and trends...",
            progress: 50,
            status: "active",
          })

          await new Promise((resolve) => setTimeout(resolve, 2000))

          send({
            step: 3,
            action: "Search volume analysis complete",
            progress: 60,
            status: "completed",
          })

          // Step 4: Competition Analysis
          send({
            step: 4,
            action: "Checking keyword difficulty and competition...",
            progress: 70,
            status: "active",
          })

          await new Promise((resolve) => setTimeout(resolve, 2000))

          send({
            step: 4,
            action: "Competition analysis complete",
            progress: 80,
            status: "completed",
          })

          // Step 5: Report Generation
          send({
            step: 5,
            action: "Generating your SEO strategy report...",
            progress: 90,
            status: "active",
          })
        }

        // Enhanced agent call with partial results streaming
        console.log("🚀 Starting AI agent generation with enhanced fallback strategy...")
        console.log("📝 Input message:", message.substring(0, 200) + "...")
        console.log("⚙️ Configuration:", { isGapAnalysis, timeoutDuration: isGapAnalysis ? 90000 : 120000 })

        let response: any
        let partialResults: any = {}
        let agentCompleted = false
        const toolResults: any = {}

        try {
          // Progressive timeout strategy
          const timeoutDuration = isGapAnalysis ? 90000 : 120000 // 90s for gap analysis, 120s for others
          const partialResultsTimeout = Math.floor(timeoutDuration * 0.75) // 75% of total time for partial results

          console.log("⏱️ Timeout configuration:", {
            total: timeoutDuration,
            partialCapture: partialResultsTimeout,
            isGapAnalysis
          })

          // Start agent generation with enhanced logging
          console.log("🤖 Initiating agent generation...")
          const seoAgent = await getSEOStrategistAgent()
          const agentPromise = seoAgent.generate([
            {
              role: "user",
              content: message,
            },
          ])

          // Set up partial results capture after 75% of timeout
          console.log("⏰ Setting up partial results timer for", partialResultsTimeout, "ms")
          const startTime = Date.now()

          const partialResultsTimer = setTimeout(async () => {
            if (!agentCompleted) {
              console.log("📊 PARTIAL RESULTS CAPTURE TRIGGERED")
              console.log("🔍 Agent completed status:", agentCompleted)
              console.log("⏱️ Time elapsed:", Date.now() - startTime, "ms")

              // Send partial results notification
              send({
                type: "partial_results",
                message: "Capturing available results before timeout...",
                progress: 85,
              })

              // Try to get any available partial data from tools that completed
              try {
                console.log("🎯 Attempting to capture partial response...")
                const partialResponse = await Promise.race([
                  agentPromise,
                  new Promise((_, reject) =>
                    setTimeout(() => {
                      console.log("❌ Partial capture timeout after 5s")
                      reject(new Error("Partial capture timeout"))
                    }, 5000)
                  )
                ])

                console.log("✅ Partial response captured:", !!partialResponse?.text)
                if (partialResponse && partialResponse.text) {
                  partialResults = {
                    text: partialResponse.text,
                    isPartial: true,
                    capturedAt: Date.now(),
                    source: "partial_capture"
                  }
                  console.log("💾 Stored partial results:", partialResults.text.substring(0, 200) + "...")
                }
              } catch (partialError) {
                console.log("❌ Could not capture partial results:", partialError.message)
                console.log("🔄 Will use fallback data instead")
              }
            } else {
              console.log("✅ Agent already completed, skipping partial capture")
            }
          }, partialResultsTimeout)

          // Main agent execution with timeout
          console.log("🏁 Starting main agent execution race...")

          // Store timeout ID so we can clear it when agent completes
          let mainTimeoutId: NodeJS.Timeout

          response = await Promise.race([
            agentPromise.then(result => {
              const completionTime = Date.now() - startTime
              console.log("✅ Agent completed successfully in", completionTime, "ms")
              agentCompleted = true
              clearTimeout(partialResultsTimer)
              clearTimeout(mainTimeoutId) // Clear the main timeout
              console.log("🧹 Cleared all timers after successful completion")
              return result
            }),
            new Promise((_, reject) => {
              mainTimeoutId = setTimeout(
                () => {
                  const timeoutTime = Date.now() - startTime
                  console.log("⏰ MAIN TIMEOUT TRIGGERED after", timeoutTime, "ms")
                  console.log("🔍 Checking for partial results:", !!partialResults.text)

                  // If we have partial results, use them instead of rejecting
                  if (partialResults.text) {
                    console.log("✅ Using partial results due to timeout")
                    console.log("📄 Partial results length:", partialResults.text.length)
                    return partialResults
                  }

                  console.log("❌ No partial results available, rejecting with timeout error")
                  reject(
                    new Error(
                      `Agent generation timeout after ${timeoutDuration / 1000} seconds`,
                    ),
                  )
                },
                timeoutDuration,
              )
            }),
          ])

          const totalTime = Date.now() - startTime
          console.log("🏆 Agent generation completed in", totalTime, "ms", agentCompleted ? "(fully)" : "(with partial results)")
        } catch (error) {
          const errorTime = Date.now() - startTime
          console.error("❌ Agent generation failed after", errorTime, "ms:", error.message)
          console.log("🔍 Error details:", error)

          // If we have partial results, use them instead of failing completely
          if (partialResults.text) {
            console.log("✅ Using captured partial results despite error")
            console.log("📄 Partial results preview:", partialResults.text.substring(0, 300) + "...")
            response = partialResults
          } else {
            // Try fallback strategy before giving up
            console.log("🔄 No partial results available, attempting fallback strategy...")
            try {
              response = await attemptFallbackStrategy(message, send)
              console.log("✅ Fallback strategy completed successfully")
            } catch (fallbackError) {
              console.error("❌ Fallback strategy also failed:", fallbackError.message)
              throw fallbackError
            }
          }
        }

        if (!isGapAnalysis) {
          send({
            step: 5,
            action: "Report generated successfully!",
            progress: 100,
            status: "completed",
          })
        }

        // Send the final response with debug info
        console.log("📤 Sending final response...")
        console.log("📊 Response type:", response.isPartial ? "partial" : response.isFallback ? "fallback" : "complete")
        console.log("📄 Response length:", response.text?.length || 0)
        console.log("🔍 Response preview:", response.text?.substring(0, 200) + "...")

        send({
          type: "final",
          response: response.text,
        })

        console.log("✅ SEO Agent request completed successfully")

        // Close the stream
        controller.close()
      } catch (error) {
        send({
          type: "error",
          error: error instanceof Error ? error.message : "Unknown error",
        })
        controller.close()
      }
    },
  })

  return new Response(stream, {
    headers: {
      "Content-Type": "text/event-stream",
      "Cache-Control": "no-cache",
      Connection: "keep-alive",
    },
  })
}

// SEO AG-UI streaming execution with memory and bridge integration
async function performSEOAGUIStreaming(
  message: string,
  conversationId: string | undefined,
  session: any,
  sendEvent: (event: any) => boolean,
  aguiAgent: any,
  mode: 'chat' | 'gap'
) {
  try {
    // Create message array in AG-UI format
    const messages = [{ role: 'user' as const, content: message }]

    // Prepare memory context for Mastra with SEO-specific data
    const memoryOptions = conversationId && session?.user?.id ? {
      memory: {
        resource: `seo_user_${session.user.id}`, // SEO-specific user memory
        thread: { id: conversationId }, // Conversation thread
      }
    } : undefined

    console.log("=== SEO AGENT AG-UI MEMORY DEBUG ===")
    console.log("Session user ID:", session?.user?.id)
    console.log("Conversation ID:", conversationId)
    console.log("SEO Mode:", mode)
    console.log("Memory options:", JSON.stringify(memoryOptions, null, 2))
    console.log("Message being sent:", message)
    console.log("====================================")

    // Import and start the SEO bridge
    try {
      const { startSEOBridge } = await import('$lib/services/seo-agui-bridge')
      startSEOBridge({
        enableTwoWaySync: true,
        preserveLegacyBehavior: true,
        enableFallbackHandling: true,
        timeoutStrategy: mode === 'gap' ? 'progressive' : 'fixed',
        debugMode: true
      })
      console.log("SEO AG-UI Bridge started successfully")
    } catch (bridgeError) {
      console.warn("Could not start SEO bridge, continuing without it:", bridgeError)
    }

    // Stream events from AG-UI agent with memory and SEO context
    for await (const event of aguiAgent.generateStream(messages, memoryOptions)) {
      // Convert AG-UI events to bridge if available
      try {
        const { seoAguiBridge } = await import('$lib/services/seo-agui-bridge')
        if (seoAguiBridge.getStatus().active) {
          seoAguiBridge.convertAGUIEventToProgress(event)
        }
      } catch (bridgeError) {
        // Bridge conversion failed, continue without it
        console.warn("Bridge conversion failed:", bridgeError)
      }

      const success = sendEvent(event)
      if (!success) {
        console.log('SEO AG-UI stream closed, stopping generation')
        break
      }
      
      // Add small delay between events for better UX
      await new Promise(resolve => setTimeout(resolve, 10))
    }

    console.log("SEO AG-UI streaming completed successfully")
  } catch (error) {
    console.error("SEO AG-UI streaming execution error:", error)
    
    // Try to handle fallback via bridge
    try {
      const { seoAguiBridge } = await import('$lib/services/seo-agui-bridge')
      if (seoAguiBridge.getStatus().active) {
        // Let the bridge handle the error with fallback
        console.log("Delegating error handling to SEO bridge")
        return
      }
    } catch (bridgeError) {
      console.warn("Bridge error handling failed:", bridgeError)
    }
    
    // If bridge isn't available, use the existing fallback
    console.log("Using legacy fallback strategy")
    try {
      const fallbackResponse = await attemptFallbackStrategy(message, sendEvent)
      sendEvent({
        type: 'TEXT_MESSAGE_CONTENT',
        messageId: `fallback_${Date.now()}`,
        delta: fallbackResponse.text,
        timestamp: new Date().toISOString()
      })
    } catch (fallbackError) {
      console.error("Fallback strategy also failed:", fallbackError)
      throw error
    }
  }
}
