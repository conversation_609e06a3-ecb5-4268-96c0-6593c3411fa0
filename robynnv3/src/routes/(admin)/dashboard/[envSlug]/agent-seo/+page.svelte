<script lang="ts">
  import { page } from "$app/stores"
  import {
    Menu,
    ChevronRight,
    PanelRightClose,
    PanelRightOpen
  } from "lucide-svelte"

  // Import legacy components
  import SEOAgentSidebar from "$lib/components/seo/SEOAgentSidebar.svelte"
  import SEOCanvas from "$lib/components/seo/SEOCanvas.svelte"
  
  // Import AG-UI components
  import SEOAGUICanvas from "$lib/components/agui/SEOAGUICanvas.svelte"
  import SEOAGUISidebar from "$lib/components/agui/SEOAGUISidebar.svelte"
  
  // Page data from server load function
  let { data } = $props()

  // Import stores and services
  import {
    isLoading,
    progressSteps,
    addMessage,
    handleRequestError,
    initializeChatProgressSteps,
    messages
  } from "$lib/stores/seo-agent-store"

  import {
    SEOAgentService
  } from "$lib/services/seo-agent-service"

  import { writable } from 'svelte/store'

  // Feature flags from server
  let useAGUI = $derived(data.featureFlags.aguiEnabled)
  let aguiStreamingEnabled = $derived(data.featureFlags.aguiStreamingEnabled)
  
  // Debug feature flags
  $effect(() => {
    console.log('=== SEO PAGE FEATURE FLAGS ===')
    console.log('AGUI Enabled:', useAGUI)
    console.log('AGUI Streaming Enabled:', aguiStreamingEnabled)
    console.log('================================')
  })

  // Sidebar state management
  const leftSidebarCollapsed = writable(false)
  const rightSidebarCollapsed = writable(false)

  // Reactive variables
  let innerWidth = $state(0)
  let isMobile = $derived(innerWidth < 768)
  let mobileMenuOpen = $state(false)
  let sidebarOpen = $state(true)
  let seoService: SEOAgentService
  let sidebarComponent: SEOAgentSidebar
  let sidebarWidth = $state(400) // Default sidebar width in pixels
  let isDragging = $state(false)
  let minSidebarWidth = 300
  let maxSidebarWidth = 800

  // Error handling state
  let requestTimeout: NodeJS.Timeout | undefined
  const REQUEST_TIMEOUT_MS = 125000 // 125 seconds timeout (slightly longer than server timeout)

  // Partial results state
  let partialResultsReceived = $state(false)
  let fallbackResultsReceived = $state(false)
  let lastPartialResponse = $state("")

  // Toggle functions
  function toggleSidebar() {
    sidebarOpen = !sidebarOpen
    rightSidebarCollapsed.set(!sidebarOpen)
  }

  // Resizable sidebar functionality
  function handleMouseDown(e: MouseEvent) {
    isDragging = true
    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
    e.preventDefault()
  }

  function handleMouseMove(e: MouseEvent) {
    if (!isDragging) return
    
    const containerRect = (e.target as Element)?.closest('.canvas-container')?.getBoundingClientRect()
    if (!containerRect) return
    
    const newWidth = containerRect.right - e.clientX
    sidebarWidth = Math.max(minSidebarWidth, Math.min(maxSidebarWidth, newWidth))
  }

  function handleMouseUp() {
    isDragging = false
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }

  // Initialize service
  $effect(() => {
    if ($page.params.envSlug) {
      seoService = new SEOAgentService($page.params.envSlug)
    }
  })

  // Responsive behavior - auto-collapse on mobile
  $effect(() => {
    if (isMobile) {
      leftSidebarCollapsed.set(true)
      rightSidebarCollapsed.set(true)
    }
  })

  // Event handlers
  async function handleSendMessage(event: CustomEvent) {
    const { message } = event.detail

    if (!message.trim() || $isLoading) return

    isLoading.set(true)

    // Set up request timeout
    requestTimeout = setTimeout(() => {
      if ($isLoading) {
        handleRequestError(new Error("Request timeout"), 'timeout')
        isLoading.set(false)
      }
    }, REQUEST_TIMEOUT_MS)

    // Initialize progress steps
    initializeChatProgressSteps()

    // Add user message
    addMessage({
      role: 'user',
      content: message
    })

    try {
      const response = await seoService.sendChatMessage(
        message,
        'summary',
        {},
        (step, status, action, _progress) => {
          // Update progress steps based on server updates
          progressSteps.update(steps =>
            steps.map(stepItem => {
              if (stepItem.id < step) {
                return { ...stepItem, status: 'completed' }
              } else if (stepItem.id === step) {
                return {
                  ...stepItem,
                  status: status === 'active' ? 'active' : stepItem.status,
                  description: action || stepItem.description
                }
              }
              return stepItem
            })
          )
        },
        (error) => {
          console.error('SEO Agent error:', error)
          // Error will be handled in the catch block
        }
      )

      // Clear timeout on successful completion
      if (requestTimeout) {
        clearTimeout(requestTimeout)
        requestTimeout = undefined
      }

      // Parse metadata if present
      const metadataPattern = /<!-- Analysis Metadata: (.*?) -->/
      const metadataMatch = response.match(metadataPattern)
      let metadata = null
      let isPartialResult = false

      if (metadataMatch) {
        try {
          metadata = JSON.parse(metadataMatch[1])
          console.log("📊 Analysis metadata:", metadata)
          // Only mark as partial if metadata explicitly indicates it
          isPartialResult = metadata?.isPartial || false
        } catch (e) {
          console.error("Error parsing metadata:", e)
          // If metadata parsing fails, don't assume it's partial
          isPartialResult = false
        }
      }

      // Parse and add assistant response
      const structuredData = SEOAgentService.parseStructuredResponse(response)
      console.log("🔍 Parsed structured data:", structuredData)
      console.log("🔍 Content strategy data:", structuredData.contentStrategy)

      // Clean response for display (remove metadata and XML tags)
      let cleanedResponse = response
      console.log("🧹 Original response length:", response.length)
      console.log("🧹 Original response preview:", response.substring(0, 200))

      cleanedResponse = cleanedResponse.replace(/<!-- Analysis Metadata:.*? -->\n?/gs, "")
      cleanedResponse = cleanedResponse.replace(/<GAP_RESULTS>.*?<\/GAP_RESULTS>/gs, "")
      cleanedResponse = cleanedResponse.replace(/<KEYWORDS>.*?<\/KEYWORDS>/gs, "")
      cleanedResponse = cleanedResponse.replace(/<NICHE_KEYWORDS>.*?<\/NICHE_KEYWORDS>/gs, "")
      cleanedResponse = cleanedResponse.replace(/<COMPANY_PROFILE>.*?<\/COMPANY_PROFILE>/gs, "")
      cleanedResponse = cleanedResponse.replace(/<CONTENT_STRATEGY>.*?<\/CONTENT_STRATEGY>/gs, "")

      console.log("🧹 Cleaned response length:", cleanedResponse.length)
      console.log("🧹 Cleaned response preview:", cleanedResponse.substring(0, 200))
      console.log("🧹 Cleaned response trimmed length:", cleanedResponse.trim().length)

      // Store partial response for potential retry
      if (isPartialResult || metadata?.isFallback) {
        lastPartialResponse = response
        partialResultsReceived = isPartialResult
        fallbackResultsReceived = metadata?.isFallback || false
      }

      // Add assistant message with structured data and metadata
      const messageToAdd = {
        role: 'assistant' as const,
        content: SEOAgentService.formatContent(cleanedResponse.trim() || response),
        data: {
          ...structuredData,
          metadata: metadata
        }
      }

      console.log("🎯 Adding message to store:", messageToAdd)
      addMessage(messageToAdd)
      console.log("🎯 Message added, current messages length should be > 0")

      // Update sidebar with response
      if (sidebarComponent) {
        const contentForSidebar = cleanedResponse.trim() || response
        console.log("📋 Content for sidebar length:", contentForSidebar.length)
        console.log("📋 Content for sidebar preview:", contentForSidebar.substring(0, 100))

        const formattedContent = SEOAgentService.formatContent(contentForSidebar)
        console.log("📋 Formatted content length:", formattedContent.length)
        console.log("📋 Formatted content preview:", formattedContent.substring(0, 100))

        await sidebarComponent.addAssistantMessage(
          formattedContent,
          structuredData
        )
      }

    } catch (error) {
      // Determine error type for better user feedback
      let errorType: 'timeout' | 'network' | 'server' | 'unknown' = 'unknown'

      if (error instanceof Error) {
        if (error.message.includes('timeout') || error.message.includes('Request timeout')) {
          errorType = 'timeout'
        } else if (error.message.includes('fetch') || error.message.includes('network')) {
          errorType = 'network'
        } else if (error.message.includes('server') || error.message.includes('500')) {
          errorType = 'server'
        }
      }

      handleRequestError(error, errorType)
    } finally {
      isLoading.set(false)
      // Clear timeout if still active
      if (requestTimeout) {
        clearTimeout(requestTimeout)
        requestTimeout = undefined
      }
    }
  }



  function handleExampleSelect(event: CustomEvent) {
    const { message } = event.detail
    if (sidebarComponent) {
      sidebarComponent.populateInput(message)
    }
  }
</script>

<style>
  .toggle-with-sidebar {
    right: 400px;
  }
  
  @media (max-width: 768px) {
    .toggle-with-sidebar {
      right: 1rem;
    }
  }
</style>

<!-- Main Page Container -->
<div class="min-h-screen bg-background">
  <!-- Header Navigation -->
  <div class="bg-background border-b-2 border-border sticky top-0 z-50 nav-blur">
    <div class="max-w-6xl mx-auto py-4" style="padding-left: 10px; padding-right: 10px;">
      <div class="flex items-center justify-between">
        <div class="flex-1 flex items-center space-x-2">
          <div
            class="w-8 h-8 flex items-center justify-center bg-primary text-primary-foreground border-2 border-border shadow-soft-sm"
          >
            <span class="font-bold text-sm">R</span>
          </div>
          <a
            href="/"
            class="text-xl font-bold text-foreground hover:opacity-70 transition-colors"
          >
            Robynn.ai
          </a>
        </div>
        <div class="flex-none">
          <ul class="hidden sm:flex items-center gap-8 font-bold">
            <li>
              <a
                href="/sign_out"
                class="text-muted-foreground hover:text-foreground transition-colors"
              >
                Sign Out
              </a>
            </li>
            <li>
              <a
                href="/dashboard/{$page.params.envSlug}"
                class="btn-professional px-6 py-2"
              >
                Dashboard
              </a>
            </li>
          </ul>

          <div class="sm:hidden">
            <button
              onclick={() => mobileMenuOpen = !mobileMenuOpen}
              class="p-2 text-muted-foreground hover:text-foreground transition-colors"
              aria-label="Toggle menu"
            >
              <Menu class="h-5 w-5" />
            </button>

            {#if mobileMenuOpen}
              <div class="absolute right-0 top-full mt-2 w-56 bg-background border-2 border-border shadow-lg rounded-lg">
                <div class="p-2">
                  <a
                    href="/sign_out"
                    class="block px-3 py-2 text-sm font-bold text-muted-foreground hover:text-foreground transition-colors"
                    onclick={() => mobileMenuOpen = false}
                  >
                    Sign Out
                  </a>
                  <a
                    href="/dashboard/{$page.params.envSlug}"
                    class="block px-3 py-2 text-sm font-bold text-muted-foreground hover:text-foreground transition-colors"
                    onclick={() => mobileMenuOpen = false}
                  >
                    Dashboard
                  </a>
                </div>
              </div>
            {/if}
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Breadcrumb Navigation -->
  <div class="bg-background border-b border-border">
    <div class="px-6 py-4">
      <nav class="flex items-center space-x-2 text-sm text-muted-foreground">
        <a
          href="/dashboard/{$page.params.envSlug}"
          class="hover:text-foreground transition-colors"
        >
          Dashboard
        </a>
        <ChevronRight class="w-4 h-4" />
        <span class="text-foreground font-medium">SEO Agent</span>
      </nav>
    </div>
  </div>

  <!-- Responsive Sidebar + Canvas Layout -->
  <div class="flex-1 flex bg-background relative canvas-container" style="height: calc(100vh - 120px);">
    <!-- Sidebar Toggle Button -->
    <button
      onclick={toggleSidebar}
      class="sidebar-toggle-btn absolute top-4 right-4 z-40 p-2 rounded-lg border border-border bg-card hover:bg-accent transition-all duration-200 shadow-sm hover:shadow-md"
      class:toggle-with-sidebar={sidebarOpen}
      aria-label="{sidebarOpen ? 'Close SEO Agent sidebar' : 'Open SEO Agent sidebar'}"
    >
      {#if sidebarOpen}
        <PanelRightClose class="w-5 h-5 text-foreground" />
      {:else}
        <PanelRightOpen class="w-5 h-5 text-foreground" />
      {/if}
    </button>

    <!-- Conditional Canvas and Sidebar Rendering -->
    {#if useAGUI && aguiStreamingEnabled}
      <!-- AG-UI Mode -->
      <!-- Main Canvas Area -->
      <SEOAGUICanvas />
      
      <!-- SEO Agent Sidebar -->
      <SEOAGUISidebar bind:isOpen={sidebarOpen} />
    {:else}
      <!-- Legacy Mode -->
      <!-- Main Canvas Area -->
      <div class="flex-1" style="width: calc(100% - {sidebarOpen ? sidebarWidth : 0}px);">
        <SEOCanvas
          leftSidebarCollapsed={false}
          rightSidebarCollapsed={!sidebarOpen}
          messages={$messages}
          showExamples={$messages.length === 0}
          on:selectExample={handleExampleSelect}
        />
      </div>

      {#if sidebarOpen}
        <!-- Resize Handle -->
        <div 
          class="resize-handle w-1 bg-border hover:bg-primary cursor-col-resize flex-shrink-0 transition-colors"
          onmousedown={handleMouseDown}
          class:bg-primary={isDragging}
        ></div>

        <!-- Sidebar -->
        <div class="flex-shrink-0" style="width: {sidebarWidth}px;">
          <SEOAgentSidebar
            bind:this={sidebarComponent}
            bind:isOpen={sidebarOpen}
            isLoading={$isLoading}
            progressSteps={$progressSteps}
            on:sendMessage={handleSendMessage}
          />
        </div>
      {/if}
    {/if}
  </div>
</div>

<svelte:head>
  <title>Keystone - AI SEO Strategist {useAGUI ? '(AG-UI)' : '(Legacy)'}</title>
</svelte:head>

<!-- Window width binding for responsive behavior -->
<svelte:window bind:innerWidth />
