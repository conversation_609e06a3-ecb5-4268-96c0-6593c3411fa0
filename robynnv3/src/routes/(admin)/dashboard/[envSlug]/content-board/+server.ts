import { json } from "@sveltejs/kit"
import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./$types"
import { contentAgent } from "$lib/agents"
import { ContentService } from "$lib/services/content-service"

// Keep the original POST handler for non-streaming requests
export const POST: RequestHandler = async ({
  request,
  locals,
  url,
  params,
}) => {
  // Verify user is authenticated
  const { session, user } = locals.auth
  if (!session || !user) {
    return json({ error: "Unauthorized" }, { status: 401 })
  }

  // Get the supabase client from locals
  const supabase = locals.supabase

  // Check if client wants streaming response
  const wantsStream = url.searchParams.get("stream") === "true"

  if (wantsStream) {
    return handleStreamingRequest(request, user, supabase, params.envSlug)
  }

  try {
    const { message, documentId, action, currentContent, selectedText, contextType } = await request.json()

    if (!message || typeof message !== "string") {
      return json({ error: "Message is required" }, { status: 400 })
    }

    console.log(
      "Content agent request received:",
      message.substring(0, 100) + "...",
      `Context type: ${contextType || 'none'}`
    )

    // Prepare context-aware message
    let contextualMessage = message
    if (currentContent && currentContent.trim()) {
      const contextLabel = contextType === 'selected' ? 'Selected text' : 'Current document content'
      contextualMessage = `${message}\n\n${contextLabel}:\n${currentContent}`
    }

    // Generate response using the content agent with timeout
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(
        () =>
          reject(
            new Error("Agent timeout after 120 seconds - try a simpler query"),
          ),
        120000,
      )
    })

    const agentPromise = contentAgent.generate([
      {
        role: "user",
        content: contextualMessage,
      },
    ])

    const response: any = await Promise.race([agentPromise, timeoutPromise])

    console.log(
      "Content agent response generated, length:",
      response.text?.length || 0,
    )

    // If documentId is provided, save the interaction to session
    if (documentId) {
      const contentService = new ContentService(supabase)
      try {
        // Get environment ID from the environment slug
        const { data: environment } = await supabase
          .from("environments")
          .select("id")
          .eq("slug", params.envSlug)
          .single()

        if (environment) {
          await contentService.createSession({
            document_id: documentId,
            user_id: user.id,
            environment_id: environment.id,
            session_data: {
              message,
              response: response.text,
              action,
              timestamp: new Date().toISOString(),
            },
          })
        }
      } catch (error) {
        console.warn("Failed to save session:", error)
      }
    }

    return json({
      response: response.text,
      documentId,
      action,
    })
  } catch (error) {
    console.error("Content agent error:", error)
    return json(
      {
        error:
          error instanceof Error ? error.message : "Unknown error occurred",
      },
      { status: 500 },
    )
  }
}

// Streaming handler for progress updates
async function handleStreamingRequest(
  request: Request,
  user: any,
  supabase: any,
  envSlug: string,
) {
  const { message, documentId, action } = await request.json()

  if (!message || typeof message !== "string") {
    return json({ error: "Message is required" }, { status: 400 })
  }

  console.log(
    "Content agent streaming request received:",
    message.substring(0, 100) + "...",
  )

  const stream = new ReadableStream({
    start(controller) {
      const encoder = new TextEncoder()

      function send(data: Record<string, unknown>) {
        const message = `data: ${JSON.stringify(data)}\n\n`
        controller.enqueue(encoder.encode(message))
      }

      // Start processing
      ;(async () => {
        try {
          send({
            step: 1,
            action: "Initializing Content Agent...",
            progress: 10,
            status: "active",
          })

          // Determine the type of request and set up appropriate steps
          const isOutlineGeneration =
            message.toLowerCase().includes("outline") ||
            action === "generate-outline"
          const isContentGeneration =
            message.toLowerCase().includes("generate") ||
            action === "generate-content"
          const isSummarization =
            message.toLowerCase().includes("summarize") ||
            action === "summarize"
          const isGrammarCheck =
            message.toLowerCase().includes("grammar") ||
            message.toLowerCase().includes("correct") ||
            action === "grammar-check"

          let steps = [
            {
              id: 1,
              title: "Initializing",
              description: "Setting up Content Agent",
            },
            {
              id: 2,
              title: "Processing",
              description: "Analyzing your request",
            },
            {
              id: 3,
              title: "Generating",
              description: "Creating content response",
            },
            {
              id: 4,
              title: "Finalizing",
              description: "Preparing final output",
            },
          ]

          if (isOutlineGeneration) {
            steps = [
              {
                id: 1,
                title: "Analyzing Topic",
                description: "Understanding your content requirements",
              },
              {
                id: 2,
                title: "Structuring",
                description: "Creating logical content structure",
              },
              {
                id: 3,
                title: "Generating Outline",
                description: "Building detailed outline",
              },
              {
                id: 4,
                title: "Formatting",
                description: "Finalizing outline format",
              },
            ]
          } else if (isContentGeneration) {
            steps = [
              {
                id: 1,
                title: "Research",
                description: "Gathering relevant information",
              },
              {
                id: 2,
                title: "Planning",
                description: "Structuring content approach",
              },
              { id: 3, title: "Writing", description: "Generating content" },
              {
                id: 4,
                title: "Review",
                description: "Polishing and finalizing",
              },
            ]
          } else if (isSummarization) {
            steps = [
              {
                id: 1,
                title: "Analysis",
                description: "Analyzing source content",
              },
              {
                id: 2,
                title: "Extraction",
                description: "Identifying key points",
              },
              {
                id: 3,
                title: "Summarizing",
                description: "Creating concise summary",
              },
              {
                id: 4,
                title: "Formatting",
                description: "Finalizing summary format",
              },
            ]
          } else if (isGrammarCheck) {
            steps = [
              {
                id: 1,
                title: "Scanning",
                description: "Analyzing text for issues",
              },
              {
                id: 2,
                title: "Checking",
                description: "Identifying grammar and style issues",
              },
              {
                id: 3,
                title: "Correcting",
                description: "Applying corrections",
              },
              { id: 4, title: "Reviewing", description: "Final quality check" },
            ]
          }

          // Send step updates
          for (let i = 0; i < steps.length - 1; i++) {
            const step = steps[i]
            send({
              step: step.id,
              action: step.description,
              progress: 20 + i * 20,
              status: "active",
            })

            // Add realistic delay
            await new Promise((resolve) =>
              setTimeout(resolve, 1000 + Math.random() * 1000),
            )
          }

          send({
            step: steps.length - 1,
            action: "Generating AI response...",
            progress: 80,
            status: "active",
          })

          // Generate the actual response
          let response: any
          try {
            const timeoutDuration = 120000 // 2 minutes

            response = await Promise.race([
              contentAgent.generate([
                {
                  role: "user",
                  content: message,
                },
              ]),
              new Promise((_, reject) =>
                setTimeout(
                  () =>
                    reject(
                      new Error(
                        `Agent generation timeout after ${timeoutDuration / 1000} seconds`,
                      ),
                    ),
                  timeoutDuration,
                ),
              ),
            ])

            console.log("Content agent generation completed")
          } catch (error) {
            console.error("Content agent generation failed:", error)
            send({
              step: steps.length,
              action: "Error occurred during generation",
              progress: 100,
              status: "error",
              error: error instanceof Error ? error.message : "Unknown error",
            })
            controller.close()
            return
          }

          // Save session if documentId provided
          if (documentId) {
            try {
              // Get environment ID from the environment slug
              const { data: environment } = await supabase
                .from("environments")
                .select("id")
                .eq("slug", envSlug)
                .single()

              if (environment) {
                const contentService = new ContentService(supabase)
                await contentService.createSession({
                  document_id: documentId,
                  user_id: user.id,
                  environment_id: environment.id,
                  session_data: {
                    message,
                    response: response.text,
                    action,
                    timestamp: new Date().toISOString(),
                    steps,
                  },
                })
              }
            } catch (error) {
              console.warn("Failed to save session:", error)
            }
          }

          send({
            step: steps.length,
            action: "Content generated successfully!",
            progress: 100,
            status: "completed",
            response: response.text,
            documentId,
            actionType: action,
          })

          controller.close()
        } catch (error) {
          console.error("Streaming error:", error)
          send({
            step: 0,
            action: "Error occurred",
            progress: 0,
            status: "error",
            error: error instanceof Error ? error.message : "Unknown error",
          })
          controller.close()
        }
      })()
    },
  })

  return new Response(stream, {
    headers: {
      "Content-Type": "text/event-stream",
      "Cache-Control": "no-cache",
      Connection: "keep-alive",
    },
  })
}

// GET handler for document operations
export const GET: RequestHandler = async ({ url, locals, params }) => {
  const { session, user } = locals.auth
  const supabase = locals.supabase
  
  if (!session || !user) {
    return json({ error: "Unauthorized" }, { status: 401 })
  }

  const documentId = url.searchParams.get("documentId")
  const action = url.searchParams.get("action")

  const contentService = new ContentService(supabase)

  try {
    if (action === "list") {
      // Get environment ID from slug
      const { data: environment } = await supabase
        .from("environments")
        .select("id")
        .eq("slug", params.envSlug)
        .single()

      if (!environment) {
        return json({ error: "Environment not found" }, { status: 404 })
      }

      const documents = await contentService.getUserDocuments(
        user.id,
        environment.id,
        {
          limit: parseInt(url.searchParams.get("limit") || "50"),
          offset: parseInt(url.searchParams.get("offset") || "0"),
          status: url.searchParams.get("status") || undefined,
          contentType: url.searchParams.get("contentType") || undefined,
        },
      )

      return json({ documents })
    }

    if (action === "get" && documentId) {
      const document = await contentService.getDocument(
        documentId,
        user.id,
      )
      if (!document) {
        return json({ error: "Document not found" }, { status: 404 })
      }
      return json({ document })
    }

    if (action === "stats") {
      // Get environment ID from slug
      const { data: environment } = await supabase
        .from("environments")
        .select("id")
        .eq("slug", params.envSlug)
        .single()

      if (!environment) {
        return json({ error: "Environment not found" }, { status: 404 })
      }

      const stats = await contentService.getDocumentStats(
        user.id,
        environment.id,
      )
      return json({ stats })
    }

    if (action === "export" && documentId) {
      const includeMetadata = url.searchParams.get("metadata") !== "false"
      
      try {
        const markdownContent = await contentService.exportDocumentAsMarkdown(
          documentId,
          user.id,
          includeMetadata
        )
        
        const document = await contentService.getDocument(documentId, user.id)
        if (!document) {
          return json({ error: "Document not found" }, { status: 404 })
        }

        const filename = `${document.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_${new Date().toISOString().split('T')[0]}.md`
        
        return new Response(markdownContent, {
          headers: {
            'Content-Type': 'text/markdown',
            'Content-Disposition': `attachment; filename="${filename}"`
          }
        })
      } catch (error) {
        console.error("Export error:", error)
        return json({ error: "Failed to export document" }, { status: 500 })
      }
    }

    if (action === "sections" && documentId) {
      try {
        const sections = await contentService.getDocumentSections(documentId, user.id)
        return json({ sections })
      } catch (error) {
        console.error("Sections fetch error:", error)
        return json({ error: "Failed to fetch sections" }, { status: 500 })
      }
    }

    return json({ error: "Invalid action" }, { status: 400 })
  } catch (error) {
    console.error("GET request error:", error)
    return json(
      { error: error instanceof Error ? error.message : "Unknown error" },
      { status: 500 },
    )
  }
}

// PUT handler for appending content and other update operations
export const PUT: RequestHandler = async ({ request, locals, params }) => {
  console.log('PUT request received:', {
    envSlug: params.envSlug,
    url: request.url,
    method: request.method
  })
  
  const { session, user } = locals.auth
  const supabase = locals.supabase
  
  if (!session || !user) {
    console.log('PUT request - No session found', { hasSession: !!session, hasUser: !!user })
    return json({ error: "Unauthorized" }, { status: 401 })
  }

  const contentService = new ContentService(supabase)

  try {
    const { action, documentId, content, agentSource, originalPrompt, mode, conversationId } = await request.json()
    
    console.log('PUT request body:', {
      action,
      documentId,
      hasContent: !!content,
      contentLength: content?.length,
      agentSource,
      hasPrompt: !!originalPrompt,
      mode,
      conversationId
    })

    if (!action) {
      return json({ error: "Action is required" }, { status: 400 })
    }

    if (action === "append" && content) {
      console.log('Server - Appending content:', {
        documentId,
        conversationId,
        contentLength: content.length,
        agentSource: agentSource || 'content-agent',
        hasPrompt: !!originalPrompt
      })
      
      try {
        let targetDocumentId = documentId
        
        // If no documentId provided, find or create Research Session document
        if (!targetDocumentId) {
          console.log('🔍 No documentId provided, finding/creating Research Session document')
          
          // Get environment ID from slug
          const { data: environment } = await supabase
            .from("environments")
            .select("id")
            .eq("slug", params.envSlug)
            .single()

          if (!environment) {
            return json({ error: "Environment not found" }, { status: 404 })
          }

          // Look for existing Research Session document
          const { data: existingDocs } = await supabase
            .from("content_documents")
            .select("id, title, content_mode")
            .eq("user_id", user.id)
            .eq("environment_id", environment.id)
            .ilike("title", "%Research Session%")
            .eq("content_mode", "accumulation")
            .order("created_at", { ascending: false })
            .limit(1)

          if (existingDocs && existingDocs.length > 0) {
            targetDocumentId = existingDocs[0].id
            console.log('✅ Found existing Research Session document:', targetDocumentId)
          } else {
            // Create new Research Session document
            console.log('🆕 Creating new Research Session document')
            const today = new Date().toLocaleDateString()
            const conversationTitle = `Research Session - ${today}`
            
            const newDoc = await contentService.createDocument({
              title: conversationTitle,
              content: "# Research Session\n\nThis document accumulates research from agent conversations.\n",
              content_type: 'document',
              content_mode: 'accumulation',
              user_id: user.id,
              environment_id: environment.id,
              conversation_status: 'active',
              conversation_count: 0,
              tags: [],
              last_activity_at: new Date().toISOString(),
              markdown_content: "",
              section_count: 0
            })
            
            if (!newDoc) {
              return json({ error: "Failed to create Research Session document" }, { status: 500 })
            }
            
            targetDocumentId = newDoc.id
            console.log('✅ Created new Research Session document:', targetDocumentId)
          }
        }
        
        const result = await contentService.appendContentToDocument(
          targetDocumentId,
          user.id,
          content,
          agentSource || 'content-agent',
          originalPrompt
        )

        if (!result) {
          return json({ error: "Failed to append content" }, { status: 500 })
        }

        console.log('Server - Content appended successfully:', {
          documentId,
          sectionId: result.section.id,
          markdownLength: result.markdownContent?.length
        })

        return json({
          success: true,
          section: result.section,
          markdownContent: result.markdownContent,
          message: "Content appended successfully"
        })
      } catch (error) {
        console.error("Append content error:", error)
        return json(
          { error: error instanceof Error ? error.message : "Failed to append content" },
          { status: 500 }
        )
      }
    }

    if (action === "switch-mode" && documentId && mode) {
      try {
        const document = await contentService.switchDocumentMode(
          documentId,
          user.id,
          mode
        )

        if (!document) {
          return json({ error: "Failed to switch document mode" }, { status: 500 })
        }

        return json({
          success: true,
          document,
          message: `Document switched to ${mode} mode`
        })
      } catch (error) {
        console.error("Switch mode error:", error)
        return json(
          { error: error instanceof Error ? error.message : "Failed to switch mode" },
          { status: 500 }
        )
      }
    }

    if (action === "regenerate-markdown" && documentId) {
      try {
        const markdownContent = await contentService.regenerateMarkdownContent(documentId)
        
        if (!markdownContent) {
          return json({ error: "Failed to regenerate markdown" }, { status: 500 })
        }

        // Update the document with the regenerated markdown
        await contentService.updateDocument(documentId, user.id, {
          markdown_content: markdownContent
        })

        return json({
          success: true,
          markdownContent,
          message: "Markdown content regenerated"
        })
      } catch (error) {
        console.error("Regenerate markdown error:", error)
        return json(
          { error: error instanceof Error ? error.message : "Failed to regenerate markdown" },
          { status: 500 }
        )
      }
    }

    return json({ error: "Invalid action" }, { status: 400 })
  } catch (error) {
    console.error("PUT request error:", error)
    return json(
      { error: error instanceof Error ? error.message : "Unknown error" },
      { status: 500 }
    )
  }
}
