// src/routes/auth/callback/+server.js
import { redirect } from "@sveltejs/kit"
import { isAuthApiError } from "@supabase/supabase-js"
import { createAuthNotifier } from "$lib/auth-notifier"

export const GET = async ({ url, locals: { supabase, slackUserSignupsWebhookUrl } }) => {
  const code = url.searchParams.get("code")
  const error_description = url.searchParams.get("error_description")

  // Handle auth errors
  if (error_description) {
    console.error("Auth callback error:", error_description)
    redirect(303, "/login/sign_in?error=auth_failed")
  }

  if (code) {
    try {
      const { data, error } = await supabase.auth.exchangeCodeForSession(code)

      if (error) {
        console.error("Session exchange error:", error)
        redirect(303, "/login/sign_in?error=session_failed")
      }

      // Check if this is an email confirmation
      if (data.session && data.user) {
        // If user just confirmed their email, redirect to sign-in with success message
        if (data.user.email_confirmed_at) {
          // Send Slack notification for email confirmation
          const authNotifier = createAuthNotifier(slackUserSignupsWebhookUrl);
          authNotifier.notifyEmailConfirmed(data.user).catch(err => 
            console.error('Failed to send email confirmation notification:', err)
          );
          
          redirect(303, "/login/sign_in?verified=true")
        }
      }
    } catch (error) {
      console.error("Auth callback error:", error)
      // If you open in another browser, need to redirect to login.
      if (isAuthApiError(error)) {
        redirect(303, "/login/sign_in?verified=true")
      } else {
        redirect(303, "/login/sign_in?error=unexpected")
      }
    }
  }

  const next = url.searchParams.get("next")
  if (next) {
    redirect(303, next)
  }

  redirect(303, "/find-env")
}
