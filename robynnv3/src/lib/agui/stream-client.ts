// AG-UI Streaming Client
// Handles client-side streaming connection to AG-UI endpoints

import type { AGUIEventUnion } from './event-types'

export interface StreamConfig {
  endpoint: string
  body?: any
  headers?: Record<string, string>
  onEvent?: (event: AGUIEventUnion) => void
  onError?: (error: Error) => void
  onComplete?: () => void
}

export class AGUIStreamClient {
  private eventSource?: EventSource
  private abortController?: AbortController
  
  async connect(config: StreamConfig): Promise<void> {
    return new Promise((resolve, reject) => {
      this.abortController = new AbortController()

      // For POST requests with body, we need to use fetch + ReadableStream
      if (config.body) {
        this.connectWithFetch(config, resolve, reject)
      } else {
        // For GET requests, we can use EventSource
        this.connectWithEventSource(config, resolve, reject)
      }
    })
  }

  private async connectWithFetch(
    config: StreamConfig, 
    resolve: () => void, 
    reject: (error: Error) => void
  ) {
    try {
      const response = await fetch(config.endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream',
          ...config.headers
        },
        body: JSON.stringify(config.body),
        credentials: 'include', // Include session cookies for authentication
        signal: this.abortController?.signal
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      if (!response.body) {
        throw new Error('Response body is null')
      }

      const reader = response.body.getReader()
      const decoder = new TextDecoder()
      let buffer = ''

      resolve() // Connection established

      while (true) {
        const { done, value } = await reader.read()
        
        if (done) {
          config.onComplete?.()
          break
        }

        buffer += decoder.decode(value, { stream: true })
        const lines = buffer.split('\n')
        buffer = lines.pop() || '' // Keep incomplete line in buffer

        for (const line of lines) {
          this.processEventLine(line.trim(), config)
        }
      }
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        // Connection was aborted, don't treat as error
        return
      }
      config.onError?.(error instanceof Error ? error : new Error('Unknown error'))
      reject(error instanceof Error ? error : new Error('Unknown error'))
    }
  }

  private connectWithEventSource(
    config: StreamConfig,
    resolve: () => void,
    reject: (error: Error) => void
  ) {
    try {
      this.eventSource = new EventSource(config.endpoint)
      
      this.eventSource.onopen = () => {
        resolve()
      }

      this.eventSource.onmessage = (event) => {
        try {
          const aguiEvent = JSON.parse(event.data) as AGUIEventUnion
          config.onEvent?.(aguiEvent)
        } catch (error) {
          console.warn('Failed to parse AG-UI event:', event.data, error)
        }
      }

      this.eventSource.onerror = (error) => {
        config.onError?.(new Error('EventSource error'))
        reject(new Error('EventSource error'))
      }

      // Handle custom event types
      this.eventSource.addEventListener('agui', (event) => {
        try {
          const aguiEvent = JSON.parse(event.data) as AGUIEventUnion
          config.onEvent?.(aguiEvent)
        } catch (error) {
          console.warn('Failed to parse AG-UI event:', event.data, error)
        }
      })

    } catch (error) {
      config.onError?.(error instanceof Error ? error : new Error('Unknown error'))
      reject(error instanceof Error ? error : new Error('Unknown error'))
    }
  }

  private processEventLine(line: string, config: StreamConfig) {
    if (!line || line.startsWith(':')) {
      return // Skip empty lines and comments
    }

    if (line.startsWith('data: ')) {
      const data = line.slice(6) // Remove 'data: ' prefix
      
      if (data === '[DONE]') {
        config.onComplete?.()
        return
      }

      try {
        const event = JSON.parse(data) as AGUIEventUnion
        config.onEvent?.(event)
      } catch (error) {
        console.warn('Failed to parse AG-UI event data:', data, error)
      }
    }
  }

  async sendControlEvent(event: AGUIEventUnion): Promise<void> {
    // For now, we'll just log control events
    // In a real implementation, this would send the control event to the server
    console.log('Sending control event:', event)
    
    // Simulate sending to server - could be a separate endpoint
    // await fetch('/api/agui/control', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(event)
    // })
  }

  disconnect(): void {
    if (this.eventSource) {
      this.eventSource.close()
      this.eventSource = undefined
    }

    if (this.abortController) {
      this.abortController.abort()
      this.abortController = undefined
    }
  }

  isConnected(): boolean {
    return (this.eventSource?.readyState === EventSource.OPEN) || !!this.abortController
  }
}

// Helper function to create and connect a stream
export async function createAGUIStream(config: StreamConfig): Promise<AGUIStreamClient> {
  const client = new AGUIStreamClient()
  await client.connect(config)
  return client
}
