// AG-UI Conversation Store
// Manages conversation state with AG-UI event stream integration

import { writable, derived, type Writable, type Readable } from 'svelte/store'
import { EventType, type AGUIEventUnion, type ConversationState } from './event-types'
import { AGUIStreamClient, type StreamConfig } from './stream-client'
import { replaceResearchData, addToolResult, tryParseResearchData, addNote, clearCanvas } from './canvas-store'

// Initial conversation state
const initialState: ConversationState = {
  messages: [],
  toolCalls: [],
  tasks: [],
  state: {},
  isLoading: false,
  isPaused: false,
  canCancel: false,
  canPause: false,
  runId: undefined,
  conversationId: undefined
}

// Create the main conversation store
export const conversationStore: Writable<ConversationState> = writable(initialState)

// Derived stores for specific data
export const messages: Readable<ConversationState['messages']> = derived(
  conversationStore,
  ($store) => $store.messages
)

export const toolCalls: Readable<ConversationState['toolCalls']> = derived(
  conversationStore,
  ($store) => $store.toolCalls
)

export const tasks: Readable<ConversationState['tasks']> = derived(
  conversationStore,
  ($store) => $store.tasks
)

export const isLoading: Readable<boolean> = derived(
  conversationStore,
  ($store) => $store.isLoading
)

export const isPaused: Readable<boolean> = derived(
  conversationStore,
  ($store) => $store.isPaused
)

export const canCancel: Readable<boolean> = derived(
  conversationStore,
  ($store) => $store.canCancel
)

export const canPause: Readable<boolean> = derived(
  conversationStore,
  ($store) => $store.canPause
)

export const currentState: Readable<Record<string, any>> = derived(
  conversationStore,
  ($store) => $store.state
)

// AG-UI Stream Manager Class
export class AGUIConversationManager {
  private streamClient?: AGUIStreamClient
  private currentMessageId?: string
  private contentBuffer = new Map<string, string>() // Buffer for smoother streaming
  private updateTimeouts = new Map<string, NodeJS.Timeout>() // Throttling timeouts

  constructor(private store: Writable<ConversationState> = conversationStore) {}

  // Send a message and start streaming
  async sendMessage(endpoint: string, message: string, context?: Record<string, any>): Promise<void> {
    // Get or generate conversation ID
    let conversationId: string
    let isFirstMessage = false
    
    this.store.update(state => {
      // Ensure state has proper structure
      const currentMessages = Array.isArray(state.messages) ? state.messages : []
      conversationId = state.conversationId || this.generateId()
      isFirstMessage = currentMessages.length === 0
      
      console.log("=== CONVERSATION STORE DEBUG ===")
      console.log("Existing conversation ID:", state.conversationId)
      console.log("Using conversation ID:", conversationId)
      console.log("Message:", message)
      console.log("Context:", context)
      console.log("Current messages length:", currentMessages.length)
      console.log("Is first message:", isFirstMessage)
      console.log("================================")
      
      return {
        ...state,
        conversationId,
        messages: [...currentMessages, {
          id: this.generateId(),
          role: 'user' as const,
          content: message,
          timestamp: new Date()
        }],
        isLoading: true
      }
    })
    
    // Clear canvas when starting a new conversation
    if (isFirstMessage) {
      console.log('=== CLEARING CANVAS FOR NEW CONVERSATION ===')
      clearCanvas()
    }

    // Prepare stream configuration
    const streamConfig: StreamConfig = {
      endpoint,
      body: {
        message, // Send message directly as expected by server
        conversationId: conversationId!, // Include conversation ID for memory
        context
      },
      onEvent: (event) => this.handleEvent(event),
      onError: (error) => this.handleError(error),
      onComplete: () => this.handleComplete()
    }

    console.log("=== STREAM CONFIG DEBUG ===")
    console.log("Endpoint:", endpoint)
    console.log("Stream body:", JSON.stringify(streamConfig.body, null, 2))
    console.log("=============================")

    try {
      // Disconnect existing stream if any
      this.disconnect()

      // Create new stream connection
      this.streamClient = new AGUIStreamClient()
      await this.streamClient.connect(streamConfig)
    } catch (error) {
      this.handleError(error instanceof Error ? error : new Error('Connection failed'))
    }
  }

  // Handle incoming AG-UI events
  private handleEvent(event: AGUIEventUnion): void {
    console.log('AG-UI Event:', event)

    switch (event.type) {
      case EventType.RUN_STARTED:
        this.store.update(state => ({
          ...state,
          runId: event.runId,
          isLoading: true,
          canCancel: true,
          canPause: true,
          isPaused: false
        }))
        break

      case EventType.TEXT_MESSAGE_START:
        this.currentMessageId = event.messageId
        // Don't create empty message - wait for first content
        console.log('Text message started:', event.messageId)
        break

      case EventType.TEXT_MESSAGE_CONTENT:
        if (event.messageId === this.currentMessageId) {
          // Update content buffer
          const currentBufferContent = this.contentBuffer.get(event.messageId) || ''
          const newBufferContent = currentBufferContent + event.delta
          this.contentBuffer.set(event.messageId, newBufferContent)
          
          // Clear existing timeout
          const existingTimeout = this.updateTimeouts.get(event.messageId)
          if (existingTimeout) {
            clearTimeout(existingTimeout)
          }
          
          // Throttle updates to make streaming smoother (every 100ms)
          const timeout = setTimeout(() => {
            this.updateMessageContent(event.messageId, newBufferContent)
            this.updateTimeouts.delete(event.messageId)
          }, 100)
          
          this.updateTimeouts.set(event.messageId, timeout)
        }
        break

      case EventType.TEXT_MESSAGE_END:
        // Clear any pending timeout and do final update
        const finalTimeout = this.updateTimeouts.get(event.messageId)
        if (finalTimeout) {
          clearTimeout(finalTimeout)
          this.updateTimeouts.delete(event.messageId)
        }
        
        // Final content update with complete buffer
        const finalContent = this.contentBuffer.get(event.messageId) || ''
        if (finalContent) {
          this.updateMessageContent(event.messageId, finalContent)
        }
        
        // Clean up buffer
        this.contentBuffer.delete(event.messageId)
        
        // Mark message as complete
        this.store.update(state => {
          // Ensure state has proper structure
          const currentMessages = Array.isArray(state.messages) ? state.messages : []
          
          return {
            ...state,
            messages: currentMessages.map(msg => 
              msg.id === event.messageId 
                ? { ...msg, status: 'complete' as const }
                : msg
            )
          }
        })
        this.currentMessageId = undefined
        break

      case EventType.TOOL_CALL_START:
        this.store.update(state => {
          // Ensure state has proper structure
          const currentToolCalls = Array.isArray(state.toolCalls) ? state.toolCalls : []
          
          return {
            ...state,
            toolCalls: [...currentToolCalls, {
              id: event.toolCallId,
              name: event.toolName,
              args: {},
              status: 'pending' as const,
              startTime: new Date()
            }]
          }
        })
        break

      case EventType.TOOL_CALL_ARGS:
        this.store.update(state => {
          // Ensure state has proper structure
          const currentToolCalls = Array.isArray(state.toolCalls) ? state.toolCalls : []
          
          return {
            ...state,
            toolCalls: currentToolCalls.map(tool =>
              tool.id === event.toolCallId
                ? { ...tool, args: event.args }
                : tool
            )
          }
        })
        break

      case EventType.TOOL_CALL_END:
        this.store.update(state => {
          // Ensure state has proper structure
          const currentToolCalls = Array.isArray(state.toolCalls) ? state.toolCalls : []
          
          return {
            ...state,
            toolCalls: currentToolCalls.map(tool =>
              tool.id === event.toolCallId
                ? { 
                    ...tool, 
                    result: event.result, 
                    status: 'completed' as const,
                    endTime: new Date(),
                    duration: tool.startTime ? Date.now() - tool.startTime.getTime() : undefined
                  }
                : tool
            )
          }
        })
        
        // Auto-add completed tool results to canvas
        if (event.result) {
          // Get current state to find tool name
          let toolName = 'unknown'
          this.store.subscribe(state => {
            const tool = state.toolCalls.find(t => t.id === event.toolCallId)
            if (tool) toolName = tool.name
          })()
          
          console.log('=== AUTO-ADDING TOOL RESULT TO CANVAS ===')
          console.log(`Tool: ${toolName}`, event.result)
          addToolResult(toolName, event.result)
        }
        break

      case EventType.TOOL_CALL_ERROR:
        this.store.update(state => {
          // Ensure state has proper structure
          const currentToolCalls = Array.isArray(state.toolCalls) ? state.toolCalls : []
          
          return {
            ...state,
            toolCalls: currentToolCalls.map(tool =>
              tool.id === event.toolCallId
                ? { ...tool, error: event.error, status: 'error' as const }
                : tool
            )
          }
        })
        break

      case EventType.STATE_SNAPSHOT:
        this.store.update(state => ({
          ...state,
          state: { ...event.snapshot }
        }))
        
        // Auto-update canvas with research data if available
        if (event.snapshot && typeof event.snapshot === 'object') {
          const researchData = tryParseResearchData(JSON.stringify(event.snapshot))
          if (researchData) {
            console.log('=== AUTO-ADDING RESEARCH TO CANVAS ===')
            console.log('Research data found in state snapshot:', researchData)
            replaceResearchData(researchData)
          }
        }
        break

      case EventType.STATE_DELTA:
        this.store.update(state => {
          const newState = { ...state.state }
          
          // Apply JSON Patch operations
          for (const delta of event.delta) {
            const pathParts = delta.path.split('/').filter(p => p !== '')
            
            if (delta.op === 'add' || delta.op === 'replace') {
              this.setNestedValue(newState, pathParts, delta.value)
            } else if (delta.op === 'remove') {
              this.removeNestedValue(newState, pathParts)
            }
          }
          
          return { ...state, state: newState }
        })
        break

      case EventType.PROGRESS_UPDATE:
        // Update progress for current message
        this.store.update(state => {
          // Ensure state has proper structure
          const currentMessages = Array.isArray(state.messages) ? state.messages : []
          
          return {
            ...state,
            messages: currentMessages.map(msg => 
              (event.messageId && msg.id === event.messageId) || 
              (!event.messageId && msg.id === this.currentMessageId)
                ? { 
                    ...msg, 
                    progress: {
                      current: event.current,
                      total: event.total,
                      currentTask: event.currentTask,
                      estimatedTimeRemaining: event.estimatedTimeRemaining,
                      stepProgress: event.stepProgress
                    }
                  }
                : msg
            )
          }
        })
        break

      case EventType.TASK_START:
        console.log('Task started:', event.taskName)
        this.store.update(state => {
          // Ensure state has proper structure
          const currentTasks = Array.isArray(state.tasks) ? state.tasks : []
          const currentMessages = Array.isArray(state.messages) ? state.messages : []
          
          // Add to global tasks list
          const newTask = {
            id: event.taskId,
            name: event.taskName,
            status: 'running' as const,
            description: event.taskDescription,
            startTime: new Date(),
            priority: event.priority,
            tags: event.tags,
            messageId: event.messageId
          }
          
          // Update current task for message and add to message tasks
          return {
            ...state,
            tasks: [...currentTasks, newTask],
            messages: currentMessages.map(msg => 
              (event.messageId && msg.id === event.messageId) || 
              (!event.messageId && msg.id === this.currentMessageId)
                ? { 
                    ...msg, 
                    progress: {
                      ...msg.progress,
                      current: msg.progress?.current || 0,
                      total: msg.progress?.total || 1,
                      currentTask: event.taskName
                    },
                    tasks: [
                      ...(msg.tasks || []),
                      {
                        id: event.taskId,
                        name: event.taskName,
                        status: 'running' as const,
                        description: event.taskDescription,
                        startTime: new Date(),
                        priority: event.priority,
                        tags: event.tags
                      }
                    ]
                  }
                : msg
            )
          }
        })
        break

      case EventType.TASK_END:
        console.log('Task ended:', event.taskId, event.status)
        this.store.update(state => {
          // Ensure state has proper structure
          const currentTasks = Array.isArray(state.tasks) ? state.tasks : []
          const currentMessages = Array.isArray(state.messages) ? state.messages : []
          
          return {
            ...state,
            tasks: currentTasks.map(task =>
              task.id === event.taskId
                ? {
                    ...task,
                    status: event.status,
                    endTime: new Date(),
                    duration: event.duration || (task.startTime ? Date.now() - task.startTime.getTime() : undefined),
                    result: event.result,
                    error: event.errorMessage
                  }
                : task
            ),
            messages: currentMessages.map(msg => ({
              ...msg,
              tasks: Array.isArray(msg.tasks) ? msg.tasks.map(task =>
                task.id === event.taskId
                  ? {
                      ...task,
                      status: event.status,
                      endTime: new Date(),
                      duration: event.duration || (task.startTime ? Date.now() - task.startTime.getTime() : undefined),
                      result: event.result,
                      error: event.errorMessage
                    }
                  : task
              ) : []
            }))
          }
        })
        break

      case EventType.RUN_FINISHED:
        this.store.update(state => ({
          ...state,
          isLoading: false,
          canCancel: false,
          canPause: false,
          isPaused: false
        }))
        break

      case EventType.RUN_CANCELLED:
        this.store.update(state => ({
          ...state,
          isLoading: false,
          canCancel: false,
          canPause: false,
          isPaused: false
        }))
        break

      case EventType.RUN_PAUSED:
        this.store.update(state => ({
          ...state,
          isPaused: true,
          canPause: false
        }))
        break

      case EventType.RUN_RESUMED:
        this.store.update(state => ({
          ...state,
          isPaused: false,
          canPause: true
        }))
        break

      case EventType.ERROR:
        console.error('AG-UI Error:', event.error)
        this.store.update(state => ({
          ...state,
          isLoading: false
        }))
        break
    }
  }

  private handleError(error: Error): void {
    console.error('AG-UI Stream Error:', error)
    this.store.update(state => ({
      ...state,
      isLoading: false
    }))
  }

  private handleComplete(): void {
    console.log('AG-UI Stream Complete')
    this.store.update(state => ({
      ...state,
      isLoading: false
    }))
  }

  // Helper methods for state manipulation
  private setNestedValue(obj: any, path: string[], value: any): void {
    let current = obj
    for (let i = 0; i < path.length - 1; i++) {
      const key = path[i]
      if (!(key in current) || typeof current[key] !== 'object') {
        current[key] = {}
      }
      current = current[key]
    }
    current[path[path.length - 1]] = value
  }

  private removeNestedValue(obj: any, path: string[]): void {
    let current = obj
    for (let i = 0; i < path.length - 1; i++) {
      const key = path[i]
      if (!(key in current)) return
      current = current[key]
    }
    delete current[path[path.length - 1]]
  }

  // Control methods
  async cancelRun(reason?: string): Promise<void> {
    if (this.streamClient) {
      try {
        // Send cancel request to server
        await this.streamClient.sendControlEvent({
          type: EventType.CANCEL_REQUEST,
          runId: this.getCurrentRunId(),
          reason
        })
      } catch (error) {
        console.error('Failed to send cancel request:', error)
        // Force disconnect if server doesn't respond
        this.disconnect()
      }
    }
  }

  async pauseRun(): Promise<void> {
    if (this.streamClient) {
      try {
        await this.streamClient.sendControlEvent({
          type: EventType.PAUSE_REQUEST,
          runId: this.getCurrentRunId()
        })
      } catch (error) {
        console.error('Failed to send pause request:', error)
      }
    }
  }

  async resumeRun(): Promise<void> {
    if (this.streamClient) {
      try {
        await this.streamClient.sendControlEvent({
          type: EventType.RESUME_REQUEST,
          runId: this.getCurrentRunId()
        })
      } catch (error) {
        console.error('Failed to send resume request:', error)
      }
    }
  }

  private getCurrentRunId(): string | undefined {
    let runId: string | undefined
    this.store.subscribe(state => runId = state.runId)()
    return runId
  }

  // Disconnect stream
  disconnect(): void {
    if (this.streamClient) {
      this.streamClient.disconnect()
      this.streamClient = undefined
    }
    
    // Clear buffers and timeouts
    this.contentBuffer.clear()
    this.updateTimeouts.forEach(timeout => clearTimeout(timeout))
    this.updateTimeouts.clear()
  }

  // Clear conversation
  clear(): void {
    this.disconnect()
    this.store.set(initialState)
  }

  // Start new conversation (clear but keep same conversation ID)
  newConversation(): void {
    this.disconnect()
    this.store.update(state => ({
      ...initialState,
      conversationId: this.generateId() // Generate new conversation ID
    }))
  }

  private generateId(): string {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  // Update message content with filtered content
  private updateMessageContent(messageId: string, rawContent: string): void {
    const filteredContent = this.filterStreamingContent(rawContent)
    
    this.store.update(state => {
      // Ensure state has proper structure
      const currentMessages = Array.isArray(state.messages) ? state.messages : []
      
      // Check if message already exists
      const existingMessage = currentMessages.find(msg => msg.id === messageId)
      
      if (existingMessage) {
        // Update existing message
        return {
          ...state,
          messages: currentMessages.map(msg => 
            msg.id === messageId 
              ? { ...msg, content: filteredContent, status: 'streaming' as const }
              : msg
          )
        }
      } else {
        // Create new message
        return {
          ...state,
          messages: [...currentMessages, {
            id: messageId,
            role: 'assistant' as const,
            content: filteredContent,
            timestamp: new Date(),
            status: 'streaming' as const
          }]
        }
      }
    })
  }

  // Filter streaming content to hide JSON blocks while preserving readable content
  private filterStreamingContent(content: string): string {
    // Buffer for streaming state - we need to be careful about partial JSON
    let filtered = content
    
    // Remove complete JSON blocks that appear to be standalone
    filtered = filtered.replace(/```json\s*[\s\S]*?\s*```/g, '')
    
    // Remove JSON objects that appear to be complete and at the end
    // Look for patterns like { ... } that are complete JSON objects
    const jsonPattern = /\{\s*"[^"]+"\s*:\s*\{[\s\S]*?\}\s*\}(?=\s*$)/g
    filtered = filtered.replace(jsonPattern, '')
    
    // Remove "Here's the structured JSON..." type lines
    filtered = filtered.replace(/Here's the structured JSON[^:]*:?\s*/gi, '')
    filtered = filtered.replace(/```json\s*/gi, '')
    filtered = filtered.replace(/```\s*/gi, '')
    
    // Clean up excessive whitespace but preserve paragraph breaks
    filtered = filtered.replace(/\n\s*\n\s*\n/g, '\n\n').trim()
    
    return filtered
  }
}

// Create default conversation manager instance
export const conversationManager = new AGUIConversationManager()

// Export helper functions for common operations
export const sendMessage = (endpoint: string, message: string, context?: Record<string, any>) => 
  conversationManager.sendMessage(endpoint, message, context)

export const clearConversation = () => conversationManager.clear()

export const disconnectStream = () => conversationManager.disconnect()

// Control operations
export const cancelRun = (reason?: string) => conversationManager.cancelRun(reason)

export const pauseRun = () => conversationManager.pauseRun()

export const resumeRun = () => conversationManager.resumeRun()

// Manual canvas operations
export const addMessageToCanvas = async (messageContent: string, title?: string) => {
  console.log('=== ADD MESSAGE TO CANVAS CALLED ===')
  console.log('Message content length:', messageContent.length)
  console.log('Message preview:', messageContent.substring(0, 200))
  
  // Try to parse as research data first
  const researchData = tryParseResearchData(messageContent)
  if (researchData) {
    console.log('=== MANUALLY ADDING RESEARCH TO CANVAS ===')
    console.log('Research data found:', researchData)
    replaceResearchData(researchData)
    
    // Format the research data nicely for database storage
    const formattedContent = formatResearchDataAsMarkdown(researchData, messageContent)
    await saveToDatabase(formattedContent, 'Research Data')
    return 'research'
  }
  
  // Otherwise add as note
  console.log('=== MANUALLY ADDING NOTE TO CANVAS ===')
  console.log('Adding as note with title:', title)
  addNote(messageContent, title || 'Research Insight')
  
  // For regular notes, extract just the meaningful content (remove JSON blocks)
  const cleanContent = extractMeaningfulContent(messageContent)
  await saveToDatabase(cleanContent, title || 'Research Insight')
  return 'note'
}

// Save content to database for persistence
async function saveToDatabase(content: string, title: string) {
  try {
    console.log('🔄 Saving to database:', { contentLength: content.length, title })
    
    // Get current page context
    const currentPath = window.location.pathname
    const pathParts = currentPath.split('/')
    const envSlug = pathParts[2] // /dashboard/[envSlug]/...
    
    if (!envSlug) {
      console.warn('❌ No environment slug found in path:', currentPath)
      return
    }
    
    // Find Research Session document by making a request to the current page's API
    const response = await fetch(`/dashboard/${envSlug}/content-board`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        action: 'append',
        documentId: null, // Will be auto-detected or created
        content: content,
        agentSource: 'agui-agent',
        originalPrompt: title
      })
    })
    
    if (!response.ok) {
      const errorText = await response.text()
      console.error('❌ Database save failed:', {
        status: response.status,
        error: errorText
      })
      return
    }
    
    const result = await response.json()
    console.log('✅ Successfully saved to database:', result)
    
  } catch (error) {
    console.error('❌ Error saving to database:', error)
  }
}

// Format research data as readable markdown
function formatResearchDataAsMarkdown(researchData: any, originalContent: string): string {
  let markdown = ''
  
  // Extract any introductory text before the JSON
  const introMatch = originalContent.match(/^(.*?)(?:```json|Here's the structured JSON)/s)
  if (introMatch && introMatch[1].trim()) {
    markdown += introMatch[1].trim() + '\n\n'
  }
  
  // Format the company data nicely
  if (researchData.targetCompany) {
    const company = researchData.targetCompany
    markdown += `# ${company.name || 'Company Research'}\n\n`
    
    if (company.description) {
      markdown += `## Overview\n${company.description}\n\n`
    }
    
    // Company details
    markdown += `## Company Details\n`
    if (company.domain) markdown += `- **Website:** ${company.domain}\n`
    if (company.industry) markdown += `- **Industry:** ${company.industry}\n`
    if (company.employees) markdown += `- **Employees:** ${company.employees}\n`
    if (company.revenue) markdown += `- **Revenue:** $${parseInt(company.revenue).toLocaleString()}\n`
    if (company.location) markdown += `- **Location:** ${company.location}\n`
    if (company.founded_year) markdown += `- **Founded:** ${company.founded_year}\n`
    markdown += '\n'
    
    // Technologies
    if (company.technologies && company.technologies.length > 0) {
      markdown += `## Technologies\n${company.technologies.join(', ')}\n\n`
    }
    
    // Headquarters info
    if (company.headquarters) {
      const hq = company.headquarters
      markdown += `## Headquarters\n`
      if (hq.city) markdown += `- **City:** ${hq.city}\n`
      if (hq.state) markdown += `- **State:** ${hq.state}\n`
      if (hq.country) markdown += `- **Country:** ${hq.country}\n`
      markdown += '\n'
    }
    
    // Contacts
    if (company.contacts && company.contacts.length > 0) {
      markdown += `## Key Contacts\n`
      company.contacts.forEach((contact: any) => {
        markdown += `### ${contact.name}\n`
        if (contact.title) markdown += `**Title:** ${contact.title}\n`
        if (contact.email) markdown += `**Email:** ${contact.email}\n`
        if (contact.linkedin_url) markdown += `**LinkedIn:** ${contact.linkedin_url}\n`
        markdown += '\n'
      })
    }
  }
  
  // Add competitors if available
  if (researchData.competitors && researchData.competitors.length > 0) {
    markdown += `## Competitors\n`
    researchData.competitors.forEach((comp: any, index: number) => {
      markdown += `${index + 1}. **${comp.name}** - ${comp.description}\n`
    })
    markdown += '\n'
  }
  
  // Add metadata
  if (researchData.metadata) {
    const meta = researchData.metadata
    markdown += `## Research Metadata\n`
    if (meta.confidence_score) markdown += `- **Confidence Score:** ${meta.confidence_score}\n`
    if (meta.data_freshness) markdown += `- **Data Freshness:** ${meta.data_freshness}\n`
    if (meta.sources_used) markdown += `- **Sources:** ${meta.sources_used.join(', ')}\n`
    markdown += '\n'
  }
  
  return markdown
}

// Extract meaningful content by removing JSON blocks and cleaning up
function extractMeaningfulContent(content: string): string {
  // Remove JSON code blocks
  let cleaned = content.replace(/```json\s*[\s\S]*?\s*```/g, '')
  
  // Remove "Here's the structured JSON..." type lines
  cleaned = cleaned.replace(/Here's the structured JSON[^:]*:/gi, '')
  
  // Clean up extra whitespace
  cleaned = cleaned.replace(/\n\s*\n\s*\n/g, '\n\n').trim()
  
  return cleaned || content // Fallback to original if cleaning went wrong
}

// Clear canvas when new conversation starts
export const clearCanvasOnNewConversation = () => {
  console.log('=== CLEARING CANVAS FOR NEW CONVERSATION ===')
  import('./canvas-store').then(({ clearCanvas }) => {
    clearCanvas()
  })
}
