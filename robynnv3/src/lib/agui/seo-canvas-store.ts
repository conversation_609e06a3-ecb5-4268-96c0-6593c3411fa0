// SEO AG-UI Canvas Store
// Extends AG-UI canvas store with SEO-specific data types and functionality

import { writable, get, type Writable } from 'svelte/store'
import type { Niche<PERSON>eyword, GapKeyword } from '$lib/stores/seo-agent-store'

// SEO-specific data interfaces
export interface SEOAnalysisData {
  companyProfile?: {
    domain: string
    industry: string
    targetAudience: string
    businessModel: string
    competitors: string[]
  }
  keywordStrategy?: {
    primaryKeywords: NicheKeyword[]
    longTailKeywords: NicheKeyword[]
    brandKeywords: string[]
    opportunityKeywords: NicheKeyword[]
  }
  competitorAnalysis?: {
    competitors: Array<{
      domain: string
      ranking: number
      keywords: string[]
      strengths: string[]
      weaknesses: string[]
    }>
  }
  contentStrategy?: {
    recommendations: Array<{
      title: string
      targetKeywords: string[]
      contentType: 'blog' | 'page' | 'product' | 'guide'
      priority: 'high' | 'medium' | 'low'
      estimatedTraffic: number
    }>
  }
  gapAnalysis?: {
    missingKeywords: GapKeyword[]
    lowerRankingKeywords: GapKeyword[]
    opportunityKeywords: GapKeyword[]
    competitorAdvantages: string[]
  }
}

// Extended canvas item types for SEO
export type SEOCanvasItem =
  | { id: string; type: 'seo-analysis'; data: SEOAnalysisData; timestamp: Date }
  | { id: string; type: 'keyword-research'; keywords: NicheKeyword[]; query: string; timestamp: Date }
  | { id: string; type: 'gap-analysis'; gaps: GapKeyword[]; competitors: string[]; timestamp: Date }
  | { id: string; type: 'content-strategy'; recommendations: any[]; timestamp: Date }
  | { id: string; type: 'research'; data: any; timestamp: Date }
  | { id: string; type: 'note'; title?: string; markdown: string; timestamp: Date }
  | { id: string; type: 'tool'; name: string; result: any; timestamp: Date }

export interface SEOCanvasState {
  items: SEOCanvasItem[]
  hasResearchData: boolean
  hasSEOAnalysis: boolean
  hasKeywordData: boolean
  hasGapAnalysis: boolean
  hasContentStrategy: boolean
  isPublishable: boolean
  
  // SEO-specific filters and views
  activeView: 'all' | 'keywords' | 'gaps' | 'content' | 'research'
  showFilters: boolean
  filters: {
    minVolume: number
    maxDifficulty: number
    competitionLevel: 'low' | 'medium' | 'high' | 'all'
    opportunityScore: number
  }
}

const initialSEOCanvasState: SEOCanvasState = {
  items: [],
  hasResearchData: false,
  hasSEOAnalysis: false,
  hasKeywordData: false,
  hasGapAnalysis: false,
  hasContentStrategy: false,
  isPublishable: false,
  activeView: 'all',
  showFilters: false,
  filters: {
    minVolume: 0,
    maxDifficulty: 100,
    competitionLevel: 'all',
    opportunityScore: 0
  }
}

// Create the main SEO canvas store
export const seoCanvasStore: Writable<SEOCanvasState> = writable(initialSEOCanvasState)

// Helper to get current state
export const getCurrentSEOCanvasState = (): SEOCanvasState => {
  return get(seoCanvasStore)
}

// Helper functions for managing SEO canvas state
export const addSEOCanvasItem = (item: Omit<SEOCanvasItem, 'timestamp'>) => {
  seoCanvasStore.update(state => {
    const newItem = { ...item, timestamp: new Date() } as SEOCanvasItem
    const updatedItems = [...state.items, newItem]
    
    // Update capability flags
    const hasResearchData = updatedItems.some(i => i.type === 'research')
    const hasSEOAnalysis = updatedItems.some(i => i.type === 'seo-analysis')
    const hasKeywordData = updatedItems.some(i => i.type === 'keyword-research')
    const hasGapAnalysis = updatedItems.some(i => i.type === 'gap-analysis')
    const hasContentStrategy = updatedItems.some(i => i.type === 'content-strategy')
    
    return {
      ...state,
      items: updatedItems,
      hasResearchData,
      hasSEOAnalysis,
      hasKeywordData,
      hasGapAnalysis,
      hasContentStrategy,
      isPublishable: updatedItems.length > 0
    }
  })
}

// Replace research data (for backward compatibility with base AG-UI)
export const replaceResearchData = (data: any) => {
  seoCanvasStore.update(state => {
    // Remove existing research items and add new one
    const nonResearchItems = state.items.filter(i => i.type !== 'research')
    const researchItem: SEOCanvasItem = {
      id: `research_${Date.now()}`,
      type: 'research',
      data,
      timestamp: new Date()
    }
    
    const updatedItems = [researchItem, ...nonResearchItems]
    
    return {
      ...state,
      items: updatedItems,
      hasResearchData: true,
      isPublishable: true
    }
  })
}

// Add SEO analysis data
export const addSEOAnalysis = (data: SEOAnalysisData, replaceExisting: boolean = true) => {
  console.log('=== ADD SEO ANALYSIS TO CANVAS ===')
  console.log('Analysis data:', data)
  console.log('Replace existing:', replaceExisting)
  
  seoCanvasStore.update(state => {
    let updatedItems = state.items
    
    if (replaceExisting) {
      // Remove existing SEO analysis
      updatedItems = state.items.filter(i => i.type !== 'seo-analysis')
    }
    
    const analysisItem: SEOCanvasItem = {
      id: `seo_analysis_${Date.now()}`,
      type: 'seo-analysis',
      data,
      timestamp: new Date()
    }
    
    updatedItems = [analysisItem, ...updatedItems]
    
    return {
      ...state,
      items: updatedItems,
      hasSEOAnalysis: true,
      isPublishable: true
    }
  })
}

// Add keyword research results
export const addKeywordResearch = (keywords: NicheKeyword[], query: string) => {
  console.log('=== ADD KEYWORD RESEARCH TO CANVAS ===')
  console.log('Keywords count:', keywords.length)
  console.log('Query:', query)
  
  const keywordItem: SEOCanvasItem = {
    id: `keywords_${Date.now()}`,
    type: 'keyword-research',
    keywords,
    query,
    timestamp: new Date()
  }
  
  addSEOCanvasItem(keywordItem)
}

// Add gap analysis results
export const addGapAnalysis = (gaps: GapKeyword[], competitors: string[]) => {
  console.log('=== ADD GAP ANALYSIS TO CANVAS ===')
  console.log('Gap keywords count:', gaps.length)
  console.log('Competitors:', competitors)
  
  const gapItem: SEOCanvasItem = {
    id: `gaps_${Date.now()}`,
    type: 'gap-analysis',
    gaps,
    competitors,
    timestamp: new Date()
  }
  
  addSEOCanvasItem(gapItem)
}

// Add content strategy
export const addContentStrategy = (recommendations: any[]) => {
  console.log('=== ADD CONTENT STRATEGY TO CANVAS ===')
  console.log('Recommendations count:', recommendations.length)
  
  const contentItem: SEOCanvasItem = {
    id: `content_${Date.now()}`,
    type: 'content-strategy',
    recommendations,
    timestamp: new Date()
  }
  
  addSEOCanvasItem(contentItem)
}

// Add note (for backward compatibility with base AG-UI)
export const addNote = (markdown: string, title?: string) => {
  console.log('=== ADD NOTE TO SEO CANVAS ===')
  console.log('Title:', title)
  console.log('Markdown length:', markdown.length)
  console.log('Markdown preview:', markdown.substring(0, 100))
  
  const noteItem: SEOCanvasItem = {
    id: `note_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    type: 'note',
    title,
    markdown,
    timestamp: new Date()
  }
  
  addSEOCanvasItem(noteItem)
  
  // Verify the item was added
  setTimeout(() => {
    const currentState = getCurrentSEOCanvasState()
    console.log('SEO Canvas state after adding note:', currentState)
    console.log('Total canvas items:', currentState.items.length)
  }, 0)
}

// Add tool result (for backward compatibility with base AG-UI)
export const addToolResult = (name: string, result: any) => {
  console.log('=== ADD TOOL RESULT TO SEO CANVAS ===')
  console.log('Tool name:', name)
  console.log('Result preview:', JSON.stringify(result).substring(0, 200))
  
  // Try to parse SEO-specific data from tool results
  const seoData = parseSEODataFromToolResult(name, result)
  if (seoData) {
    console.log('=== PARSED SEO DATA FROM TOOL RESULT ===')
    console.log('SEO data type:', seoData.type)
    
    switch (seoData.type) {
      case 'keywords':
        addKeywordResearch(seoData.keywords, seoData.query || '')
        break
      case 'gaps':
        addGapAnalysis(seoData.gaps, seoData.competitors || [])
        break
      case 'analysis':
        addSEOAnalysis(seoData.analysis)
        break
      default:
        // Fallback to generic tool result
        addSEOCanvasItem({
          id: `tool_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          type: 'tool',
          name,
          result
        })
    }
  } else {
    // Generic tool result
    addSEOCanvasItem({
      id: `tool_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: 'tool',
      name,
      result
    })
  }
}

// Parse SEO data from tool results
function parseSEODataFromToolResult(toolName: string, result: any): any {
  try {
    // DataForSEO keyword results
    if (toolName.includes('dataForSeo') && result?.tasks?.[0]?.result) {
      const items = result.tasks[0].result
      const keywords: NicheKeyword[] = []
      
      for (const item of items) {
        if (item.keyword && item.search_volume !== undefined) {
          keywords.push({
            keyword: item.keyword,
            search_volume: item.search_volume || 0,
            difficulty: item.keyword_difficulty || 0,
            competition: item.competition || 'unknown',
            cpc: item.cpc || 0,
            opportunity_score: calculateOpportunityScore(item)
          })
        }
      }
      
      if (keywords.length > 0) {
        return {
          type: 'keywords',
          keywords,
          query: result.query || toolName
        }
      }
    }
    
    // Gap analysis results
    if (toolName.includes('gap') && result?.gaps) {
      return {
        type: 'gaps',
        gaps: result.gaps,
        competitors: result.competitors || []
      }
    }
    
    // General SEO analysis
    if (result?.analysis || result?.seoData) {
      return {
        type: 'analysis',
        analysis: result.analysis || result.seoData
      }
    }
    
  } catch (error) {
    console.error('Error parsing SEO data from tool result:', error)
  }
  
  return null
}

// Calculate opportunity score for keywords
function calculateOpportunityScore(item: any): number {
  const volume = item.search_volume || 0
  const difficulty = item.keyword_difficulty || 100
  const cpc = item.cpc || 0
  
  // Enhanced scoring formula
  if (volume === 0) return 0
  
  // Base score: volume vs difficulty ratio
  let score = (volume / Math.max(difficulty, 1)) * 10
  
  // CPC bonus (higher CPC = more commercial value)
  if (cpc > 0) {
    score *= (1 + Math.log(cpc + 1) / 10)
  }
  
  // Competition penalty
  const competition = item.competition || 'unknown'
  if (competition === 'low') score *= 1.2
  else if (competition === 'medium') score *= 1.0
  else if (competition === 'high') score *= 0.8
  
  return Math.round(Math.max(score, 0))
}

// Remove canvas item
export const removeSEOCanvasItem = (itemId: string) => {
  seoCanvasStore.update(state => {
    const updatedItems = state.items.filter(item => item.id !== itemId)
    
    // Update capability flags
    const hasResearchData = updatedItems.some(i => i.type === 'research')
    const hasSEOAnalysis = updatedItems.some(i => i.type === 'seo-analysis')
    const hasKeywordData = updatedItems.some(i => i.type === 'keyword-research')
    const hasGapAnalysis = updatedItems.some(i => i.type === 'gap-analysis')
    const hasContentStrategy = updatedItems.some(i => i.type === 'content-strategy')
    
    return {
      ...state,
      items: updatedItems,
      hasResearchData,
      hasSEOAnalysis,
      hasKeywordData,
      hasGapAnalysis,
      hasContentStrategy,
      isPublishable: updatedItems.length > 0
    }
  })
}

// Clear canvas
export const clearCanvas = () => {
  console.log('=== CLEARING SEO CANVAS ===')
  seoCanvasStore.set(initialSEOCanvasState)
}

// Update canvas view
export const setSEOCanvasView = (view: SEOCanvasState['activeView']) => {
  seoCanvasStore.update(state => ({
    ...state,
    activeView: view
  }))
}

// Toggle filters
export const toggleSEOFilters = () => {
  seoCanvasStore.update(state => ({
    ...state,
    showFilters: !state.showFilters
  }))
}

// Update filters
export const updateSEOFilters = (filters: Partial<SEOCanvasState['filters']>) => {
  seoCanvasStore.update(state => ({
    ...state,
    filters: { ...state.filters, ...filters }
  }))
}

// Get filtered items based on current view and filters
export const getFilteredSEOItems = (): SEOCanvasItem[] => {
  const state = getCurrentSEOCanvasState()
  let items = state.items
  
  // Filter by view
  if (state.activeView !== 'all') {
    switch (state.activeView) {
      case 'keywords':
        items = items.filter(i => i.type === 'keyword-research')
        break
      case 'gaps':
        items = items.filter(i => i.type === 'gap-analysis')
        break
      case 'content':
        items = items.filter(i => i.type === 'content-strategy')
        break
      case 'research':
        items = items.filter(i => i.type === 'research' || i.type === 'seo-analysis')
        break
    }
  }
  
  // Apply filters if enabled
  if (state.showFilters) {
    items = items.filter(item => {
      if (item.type === 'keyword-research') {
        return item.keywords.some(keyword => 
          keyword.search_volume >= state.filters.minVolume &&
          keyword.difficulty <= state.filters.maxDifficulty &&
          (state.filters.competitionLevel === 'all' || keyword.competition === state.filters.competitionLevel) &&
          (keyword.opportunity_score || 0) >= state.filters.opportunityScore
        )
      }
      return true // Other item types pass through
    })
  }
  
  return items
}

// Get all keywords from canvas
export const getAllKeywords = (): NicheKeyword[] => {
  const state = getCurrentSEOCanvasState()
  const keywords: NicheKeyword[] = []
  
  state.items.forEach(item => {
    if (item.type === 'keyword-research') {
      keywords.push(...item.keywords)
    }
  })
  
  return keywords
}

// Get all gap keywords from canvas
export const getAllGapKeywords = (): GapKeyword[] => {
  const state = getCurrentSEOCanvasState()
  const gaps: GapKeyword[] = []
  
  state.items.forEach(item => {
    if (item.type === 'gap-analysis') {
      gaps.push(...item.gaps)
    }
  })
  
  return gaps
}

// Export canvas data for publishing/sharing
export const exportSEOCanvasData = () => {
  const state = getCurrentSEOCanvasState()
  
  return {
    timestamp: new Date(),
    summary: {
      totalItems: state.items.length,
      hasResearchData: state.hasResearchData,
      hasSEOAnalysis: state.hasSEOAnalysis,
      hasKeywordData: state.hasKeywordData,
      hasGapAnalysis: state.hasGapAnalysis,
      hasContentStrategy: state.hasContentStrategy
    },
    data: {
      keywords: getAllKeywords(),
      gaps: getAllGapKeywords(),
      items: state.items
    }
  }
}

// Utility function to try parsing JSON from text (for backward compatibility)
export const tryParseResearchData = (text: string): any => {
  try {
    // Look for JSON code blocks first
    const jsonMatch = text.match(/```json\s*([\s\S]*?)\s*```/)
    if (jsonMatch) {
      const parsed = JSON.parse(jsonMatch[1])
      if (looksLikeSEOData(parsed)) {
        return parsed
      }
    }
    
    // Try parsing the entire text as JSON
    const parsed = JSON.parse(text)
    if (looksLikeSEOData(parsed)) {
      return parsed
    }
  } catch (error) {
    // Not valid JSON or not SEO data
  }
  
  return null
}

// Check if an object looks like SEO research data
const looksLikeSEOData = (obj: any): boolean => {
  if (!obj || typeof obj !== 'object') return false
  
  // Check for common SEO data properties
  const hasSEOProperties = 
    obj.keywords || 
    obj.gaps ||
    obj.analysis ||
    obj.seoStrategy ||
    obj.competitorAnalysis ||
    obj.contentStrategy ||
    obj.keywordResearch
  
  return Boolean(hasSEOProperties)
}
