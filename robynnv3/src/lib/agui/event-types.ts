// AG-UI Event Types
// Based on AG-UI protocol specification

export enum EventType {
  // Runtime events
  RUN_STARTED = 'RUN_STARTED',
  RUN_FINISHED = 'RUN_FINISHED',
  RUN_CANCELLED = 'RUN_CANCELLED',
  RUN_PAUSED = 'RUN_PAUSED',
  RUN_RESUMED = 'RUN_RESUMED',
  
  // Message events
  TEXT_MESSAGE_START = 'TEXT_MESSAGE_START',
  TEXT_MESSAGE_CONTENT = 'TEXT_MESSAGE_CONTENT',
  TEXT_MESSAGE_END = 'TEXT_MESSAGE_END',
  
  // Tool call events
  TOOL_CALL_START = 'TOOL_CALL_START',
  TOOL_CALL_ARGS = 'TOOL_CALL_ARGS',
  TOOL_CALL_END = 'TOOL_CALL_END',
  TOOL_CALL_ERROR = 'TOOL_CALL_ERROR',
  
  // Progress events
  PROGRESS_UPDATE = 'PROGRESS_UPDATE',
  TASK_START = 'TASK_START',
  TASK_END = 'TASK_END',
  
  // Control events
  CANCEL_REQUEST = 'CANCEL_REQUEST',
  PAUSE_REQUEST = 'PAUSE_REQUEST',
  RESUME_REQUEST = 'RESUME_REQUEST',
  
  // State events
  STATE_SNAPSHOT = 'STATE_SNAPSHOT',
  STATE_DELTA = 'STATE_DELTA',
  
  // Error events
  ERROR = 'ERROR'
}

// Base event interface
export interface AGUIEvent {
  type: EventType
  timestamp?: string
  runId?: string
}

// Text message events
export interface TextMessageStartEvent extends AGUIEvent {
  type: EventType.TEXT_MESSAGE_START
  messageId: string
}

export interface TextMessageContentEvent extends AGUIEvent {
  type: EventType.TEXT_MESSAGE_CONTENT
  messageId: string
  delta: string
}

export interface TextMessageEndEvent extends AGUIEvent {
  type: EventType.TEXT_MESSAGE_END
  messageId: string
}

// Tool call events
export interface ToolCallStartEvent extends AGUIEvent {
  type: EventType.TOOL_CALL_START
  toolCallId: string
  toolName: string
}

export interface ToolCallArgsEvent extends AGUIEvent {
  type: EventType.TOOL_CALL_ARGS
  toolCallId: string
  args: Record<string, any>
}

export interface ToolCallEndEvent extends AGUIEvent {
  type: EventType.TOOL_CALL_END
  toolCallId: string
  result: any
}

export interface ToolCallErrorEvent extends AGUIEvent {
  type: EventType.TOOL_CALL_ERROR
  toolCallId: string
  error: string
}

// Progress events
export interface ProgressUpdateEvent extends AGUIEvent {
  type: EventType.PROGRESS_UPDATE
  messageId?: string
  current: number
  total: number
  currentTask?: string
  estimatedTimeRemaining?: number
  stepProgress?: {
    step: number
    totalSteps: number
    stepName: string
    stepDescription?: string
  }
}

export interface TaskStartEvent extends AGUIEvent {
  type: EventType.TASK_START
  taskId: string
  taskName: string
  messageId?: string
  taskDescription?: string
  estimatedDuration?: number
  priority?: 'low' | 'medium' | 'high'
  tags?: string[]
}

export interface TaskEndEvent extends AGUIEvent {
  type: EventType.TASK_END
  taskId: string
  status: 'success' | 'error' | 'cancelled'
  messageId?: string
  duration?: number
  result?: any
  errorMessage?: string
}

// State events
export interface StateSnapshotEvent extends AGUIEvent {
  type: EventType.STATE_SNAPSHOT
  snapshot: Record<string, any>
}

export interface StateDeltaEvent extends AGUIEvent {
  type: EventType.STATE_DELTA
  delta: Array<{
    op: 'add' | 'remove' | 'replace'
    path: string
    value?: any
  }>
}

// Runtime events
export interface RunStartedEvent extends AGUIEvent {
  type: EventType.RUN_STARTED
  runId: string
}

export interface RunFinishedEvent extends AGUIEvent {
  type: EventType.RUN_FINISHED
  runId: string
  status: 'success' | 'error' | 'cancelled'
}

export interface RunCancelledEvent extends AGUIEvent {
  type: EventType.RUN_CANCELLED
  runId: string
  reason?: string
}

export interface RunPausedEvent extends AGUIEvent {
  type: EventType.RUN_PAUSED
  runId: string
}

export interface RunResumedEvent extends AGUIEvent {
  type: EventType.RUN_RESUMED
  runId: string
}

// Control events
export interface CancelRequestEvent extends AGUIEvent {
  type: EventType.CANCEL_REQUEST
  runId?: string
  taskId?: string
  reason?: string
}

export interface PauseRequestEvent extends AGUIEvent {
  type: EventType.PAUSE_REQUEST
  runId?: string
}

export interface ResumeRequestEvent extends AGUIEvent {
  type: EventType.RESUME_REQUEST
  runId?: string
}

// Error events
export interface ErrorEvent extends AGUIEvent {
  type: EventType.ERROR
  error: string
  code?: string
}

// Union type for all events
export type AGUIEventUnion = 
  | TextMessageStartEvent
  | TextMessageContentEvent  
  | TextMessageEndEvent
  | ToolCallStartEvent
  | ToolCallArgsEvent
  | ToolCallEndEvent
  | ToolCallErrorEvent
  | ProgressUpdateEvent
  | TaskStartEvent
  | TaskEndEvent
  | StateSnapshotEvent
  | StateDeltaEvent
  | RunStartedEvent
  | RunFinishedEvent
  | RunCancelledEvent
  | RunPausedEvent
  | RunResumedEvent
  | CancelRequestEvent
  | PauseRequestEvent
  | ResumeRequestEvent
  | ErrorEvent

// Helper types
export interface ConversationState {
  messages: Array<{
    id: string
    role: 'user' | 'assistant'
    content: string
    timestamp: Date
    status?: 'draft' | 'streaming' | 'complete'
    progress?: {
      current: number
      total: number
      currentTask?: string
      estimatedTimeRemaining?: number
      stepProgress?: {
        step: number
        totalSteps: number
        stepName: string
        stepDescription?: string
      }
    }
    tasks?: Array<{
      id: string
      name: string
      status: 'pending' | 'running' | 'success' | 'error' | 'cancelled'
      description?: string
      startTime?: Date
      endTime?: Date
      duration?: number
      priority?: 'low' | 'medium' | 'high'
      tags?: string[]
      result?: any
      error?: string
    }>
  }>
  toolCalls: Array<{
    id: string
    name: string
    args: Record<string, any>
    result?: any
    error?: string
    status: 'pending' | 'completed' | 'error'
    startTime?: Date
    endTime?: Date
    duration?: number
  }>
  tasks: Array<{
    id: string
    name: string
    status: 'pending' | 'running' | 'success' | 'error' | 'cancelled'
    description?: string
    startTime?: Date
    endTime?: Date
    duration?: number
    priority?: 'low' | 'medium' | 'high'
    tags?: string[]
    result?: any
    error?: string
    messageId?: string
  }>
  state: Record<string, any>
  isLoading: boolean
  isPaused: boolean
  canCancel: boolean
  canPause: boolean
  runId?: string
  conversationId?: string // Add conversation ID for memory tracking
}
