import type { User } from "@supabase/supabase-js"

export type AuthEventType = 'signup' | 'signin' | 'email_confirmed' | 'profile_created'

export type AuthNotificationData = {
  eventType: AuthEventType
  user: User
  metadata?: {
    timestamp: string
    userAgent?: string
    ipAddress?: string
    signupMethod?: 'email' | 'oauth'
    provider?: string
  }
}

/**
 * Auth Notifier Service
 * 
 * Sends formatted notifications to Slack for authentication events.
 * Follows existing patterns from contact-notifier.ts and slack-webhook-tool.ts.
 */
export class AuthNotifier {
  private webhookUrl: string | undefined

  constructor(webhookUrl: string | undefined) {
    this.webhookUrl = webhookUrl
  }

  /**
   * Send auth event notification to Slack
   */
  async notify(data: AuthNotificationData): Promise<void> {
    if (!this.webhookUrl) {
      console.log('🔍 AUTH_NOTIFIER: No webhook URL configured, skipping notification')
      return
    }

    try {
      console.log(`🔍 AUTH_NOTIFIER: Sending ${data.eventType} notification for user ${data.user.email}`)
      
      const message = this.formatSlackMessage(data)
      
      await fetch(this.webhookUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(message)
      })

      console.log(`✅ AUTH_NOTIFIER: Successfully sent ${data.eventType} notification`)
    } catch (error) {
      console.error(`❌ AUTH_NOTIFIER: Failed to send ${data.eventType} notification:`, error)
      // Non-blocking - we don't want auth flow to fail due to Slack issues
    }
  }

  /**
   * Format Slack message based on auth event type
   */
  private formatSlackMessage(data: AuthNotificationData) {
    const { eventType, user, metadata } = data
    const timestamp = metadata?.timestamp || new Date().toISOString()
    const formattedTime = new Date(timestamp).toLocaleString('en-US', {
      timeZone: 'America/New_York',
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })

    const baseFields = [
      {
        type: "mrkdwn",
        text: `*Email:*\n${user.email || 'N/A'}`
      },
      {
        type: "mrkdwn",
        text: `*User ID:*\n${user.id}`
      },
      {
        type: "mrkdwn",
        text: `*Time:*\n${formattedTime} EST`
      },
      {
        type: "mrkdwn",
        text: `*Created:*\n${user.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A'}`
      }
    ]

    switch (eventType) {
      case 'signup':
        return {
          text: "New user signed up",
          blocks: [
            {
              type: "header",
              text: {
                type: "plain_text",
                text: "🎉 New User Signup"
              }
            },
            {
              type: "section",
              fields: [
                ...baseFields,
                {
                  type: "mrkdwn",
                  text: `*Method:*\n${metadata?.signupMethod || 'email'}`
                },
                {
                  type: "mrkdwn",
                  text: `*Provider:*\n${metadata?.provider || 'email'}`
                }
              ]
            },
            {
              type: "context",
              elements: [
                {
                  type: "mrkdwn",
                  text: `Email confirmation required: ${user.email_confirmed_at ? 'No (already confirmed)' : 'Yes'}`
                }
              ]
            }
          ]
        }

      case 'signin':
        return {
          text: "User signed in",
          blocks: [
            {
              type: "header",
              text: {
                type: "plain_text",
                text: "🔐 User Sign In"
              }
            },
            {
              type: "section",
              fields: baseFields
            },
            {
              type: "context",
              elements: [
                {
                  type: "mrkdwn",
                  text: `Last sign in: ${user.last_sign_in_at ? new Date(user.last_sign_in_at).toLocaleString() : 'First time'}`
                }
              ]
            }
          ]
        }

      case 'email_confirmed':
        return {
          text: "User confirmed email",
          blocks: [
            {
              type: "header",
              text: {
                type: "plain_text",
                text: "✅ Email Confirmed"
              }
            },
            {
              type: "section",
              fields: [
                ...baseFields,
                {
                  type: "mrkdwn",
                  text: `*Confirmed At:*\n${user.email_confirmed_at ? new Date(user.email_confirmed_at).toLocaleString() : 'N/A'}`
                }
              ]
            },
            {
              type: "context",
              elements: [
                {
                  type: "mrkdwn",
                  text: "User can now access the application"
                }
              ]
            }
          ]
        }

      case 'profile_created':
        return {
          text: "User completed profile setup",
          blocks: [
            {
              type: "header",
              text: {
                type: "plain_text",
                text: "👤 Profile Created"
              }
            },
            {
              type: "section",
              fields: baseFields
            },
            {
              type: "context",
              elements: [
                {
                  type: "mrkdwn",
                  text: "User has completed onboarding and created their profile"
                }
              ]
            }
          ]
        }

      default:
        return {
          text: `Authentication event: ${eventType}`,
          blocks: [
            {
              type: "section",
              text: {
                type: "mrkdwn",
                text: `*Auth Event:* ${eventType}\n*User:* ${user.email}\n*Time:* ${formattedTime} EST`
              }
            }
          ]
        }
    }
  }

  /**
   * Helper method to notify signup events
   */
  async notifySignup(user: User, signupMethod: 'email' | 'oauth' = 'email', provider?: string): Promise<void> {
    await this.notify({
      eventType: 'signup',
      user,
      metadata: {
        timestamp: new Date().toISOString(),
        signupMethod,
        provider
      }
    })
  }

  /**
   * Helper method to notify signin events
   */
  async notifySignin(user: User): Promise<void> {
    await this.notify({
      eventType: 'signin',
      user,
      metadata: {
        timestamp: new Date().toISOString()
      }
    })
  }

  /**
   * Helper method to notify email confirmation events
   */
  async notifyEmailConfirmed(user: User): Promise<void> {
    await this.notify({
      eventType: 'email_confirmed',
      user,
      metadata: {
        timestamp: new Date().toISOString()
      }
    })
  }

  /**
   * Helper method to notify profile creation events
   */
  async notifyProfileCreated(user: User): Promise<void> {
    await this.notify({
      eventType: 'profile_created',
      user,
      metadata: {
        timestamp: new Date().toISOString()
      }
    })
  }
}

/**
 * Create an AuthNotifier instance with the user signups webhook URL
 */
export function createAuthNotifier(webhookUrl: string | undefined): AuthNotifier {
  return new AuthNotifier(webhookUrl)
}

/**
 * Test the auth notification system
 */
export async function testAuthNotification(webhookUrl: string | undefined): Promise<boolean> {
  if (!webhookUrl) {
    console.log('🔍 AUTH_NOTIFIER: No webhook URL provided for testing')
    return false
  }

  try {
    const testUser: User = {
      id: 'test-user-id',
      email: '<EMAIL>',
      created_at: new Date().toISOString(),
      last_sign_in_at: new Date().toISOString(),
      email_confirmed_at: new Date().toISOString(),
      aud: 'authenticated',
      app_metadata: {},
      user_metadata: {}
    }

    const notifier = new AuthNotifier(webhookUrl)
    await notifier.notify({
      eventType: 'signup',
      user: testUser,
      metadata: {
        timestamp: new Date().toISOString(),
        signupMethod: 'email'
      }
    })

    return true
  } catch (error) {
    console.error('🔍 AUTH_NOTIFIER: Test notification failed:', error)
    return false
  }
}