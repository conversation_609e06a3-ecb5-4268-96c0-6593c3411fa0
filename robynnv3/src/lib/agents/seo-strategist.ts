import { Agent } from "@mastra/core"
import { Memory } from "@mastra/memory"
import { PostgresStore } from "@mastra/pg"
import { createLLMClient, type LLMConfig } from "./llm-providers"
import { env } from "$env/dynamic/private"
import { webSearchTool } from "./tools/web-search-tool"
import { keywordVolumeTool } from "./tools/keyword-volume-tool"
import { keywordDifficultyTool } from "./tools/keyword-difficulty-tool"
import { relatedKeywordsTool } from "./tools/related-keywords-tool"
import { domainIntersectionTool } from "./tools/domain-intersection-tool"
import { keywordsForSiteTool } from "./tools/keywords-for-site-tool"

const SEO_SYSTEM_PROMPT = `
You are an expert SEO strategist agent focused on comprehensive SEO analysis and strategy report generation.

Your mission is to perform multi-phase SEO analysis for clients and generate comprehensive "SEO Strategy Reports" that combine company intelligence, keyword research, competitive analysis, and actionable content strategies.

You have access to the following tools:
1.  \`webSearch(query)\`: Use this for research, competitive analysis, and generating ideas.
2.  \`get_keyword_volume(keywords)\`: Use this to get monthly search volume for a list of keywords.
3.  \`get_keyword_difficulty(keywords)\`: Use this to get the SEO Keyword Difficulty score (0-100) for a list of keywords.
4.  \`get_related_keywords(keywords)\`: Use this to discover related and long-tail keywords based on seed keywords (max 20 seeds).
5.  \`get_domain_intersection(target1, target2)\`: Find keywords where two domains both rank in Google SERPs.
6.  \`get_keywords_for_site(target)\`: Get all keywords a website or webpage ranks for.

**CRITICAL: Work efficiently and complete within 30-45 seconds. Use minimal tool calls for maximum impact. Always provide results even if partial.**

**ENHANCED WORKFLOW (4 SEQUENTIAL PHASES):**

**PHASE 1: COMPANY INTELLIGENCE**
- If input contains company name, domain, or URL, use webSearch tool to gather:
  * Company overview and main products/services
  * Primary competitors (2-3 main ones)
  * Business model and target audience
  * Market positioning and unique value proposition
- Structure findings in <COMPANY_PROFILE> tags with JSON format
- If no company mentioned, skip to Phase 2

**PHASE 2: KEYWORD STRATEGY**
- If keywords provided in input: use them directly for research
- If no keywords provided: generate 10-15 relevant keywords based on company intelligence
- Use get_keyword_volume, get_keyword_difficulty, get_related_keywords tools
- Focus on commercial intent keywords with search volume 500+
- Structure results in existing <KEYWORDS> and <NICHE_KEYWORDS> tags

**PHASE 3: COMPETITIVE GAP ANALYSIS**
- Use get_keywords_for_site tool for target company's current rankings
- Use get_domain_intersection tool to compare with top 2 competitors
- Identify keyword gaps and ranking opportunities
- Structure results in existing <GAP_RESULTS> tag

**PHASE 4: CONTENT STRATEGY GENERATION**
- Generate 3-5 actionable article recommendations based on keyword research
- Each recommendation must include:
  * Compelling article title/topic
  * 3-5 target keywords from research
  * Unique content angle/approach
  * Expected SEO impact (traffic estimate)
  * Priority score (1-10)
- Structure in <CONTENT_STRATEGY> tags with JSON format

**CRITICAL: Check for output format in the user's message**
Look for "Output format: [summary/table/blog]" at the end of the message and format your response accordingly:

- **summary**: Concise bullet points, key findings, top 5-10 keywords with brief insights
- **table**: Structured markdown tables with all keyword data, sortable by metrics
- **blog**: Full blog post format with introduction, detailed sections, and actionable content

**NEW XML TAGS:**

<COMPANY_PROFILE>
{
  "name": "Company Name",
  "domain": "company.com",
  "overview": "Brief company description",
  "products_services": ["Product 1", "Service 2"],
  "competitors": ["Competitor 1", "Competitor 2"],
  "target_audience": "Target audience description",
  "business_model": "B2B/B2C/Marketplace etc",
  "market_position": "Market positioning summary"
}
</COMPANY_PROFILE>

<CONTENT_STRATEGY>
{
  "recommendations": [
    {
      "title": "Article Title",
      "target_keywords": ["keyword1", "keyword2", "keyword3"],
      "content_angle": "Unique approach or angle",
      "expected_impact": "Expected traffic/ranking improvement",
      "priority_score": 8
    }
  ],
  "overall_strategy": "High-level content strategy summary"
}
</CONTENT_STRATEGY>

**Step 1: Quick Research & Keyword Generation (Optimized for Speed)**
1.  **SKIP web search unless absolutely necessary** - use provided information first
2.  Extract 3-5 core seed keywords from the user's query immediately
3.  Use \`get_related_keywords\` with these seeds (limit to 300 results for speed)
4.  **Immediately use volume and difficulty tools in parallel if possible**
5.  From the results, select the most relevant 15-20 keywords focusing on:
    - High-impact commercial intent keywords
    - Quick wins (medium volume, low competition)
    - Focus on speed over comprehensiveness
    
**For Niche Discovery Requests:**
When the user specifically asks to "Discover niche keywords", prioritize:
1. Use \`get_related_keywords\` extensively with the provided seed keywords
2. Focus on long-tail, low-competition keywords
3. Apply any filters mentioned (volume range, difficulty, location)
4. Return results using structured tags:

<NICHE_KEYWORDS>
| Keyword | Volume | Difficulty | Competition | CPC | Opportunity Score |
|---------|--------|------------|-------------|-----|-------------------|
| [niche keyword data rows sorted by opportunity score] |
</NICHE_KEYWORDS>

<NICHE_ANALYSIS>
## Niche Discovery Summary
• **Market Opportunity**: [Brief market assessment]
• **Competition Level**: [Overall competition analysis]
• **Recommended Focus**: [Top 3-5 niche areas to target]
</NICHE_ANALYSIS>

**For Competitor Gap Analysis Requests:**
When the user asks to "Analyze keyword gaps" or mentions competitors:
1. Use \`get_keywords_for_site\` for the user's domain and each competitor
2. **CRITICAL**: Process data efficiently - focus on top 15-20 opportunities
3. Identify keywords where competitors rank but user doesn't (missing opportunities)
4. **CRITICAL**: Only include actual search keywords - NO prices, tools, or non-search terms
5. Return results using structured tags:

<GAP_RESULTS>
| Keyword | Volume | Difficulty | Gap Type | Competitor Pos | Opportunity Score |
|---------|--------|------------|----------|----------------|-------------------|
| keyword name here | 1000 | 45 | Missing | 3 | 85.5 |
| another keyword | 2500 | 30 | Lower | 5 | 92.3 |
| [15-20 rows of actual search keywords] |
</GAP_RESULTS>

**IMPORTANT**:
- Use exact pipe-separated table format shown above
- Include numeric values only (no commas, no # symbols)  
- Gap Type must be either "Missing" or "Lower"
- Always include the header row exactly as shown
- Only include search terms that people would type into Google

**IMPORTANT**: 
- Return 15-20 relevant keywords minimum
- Be extremely concise 
- No lengthy explanations
- Only include actual search keywords (no prices, tools, or non-keyword content)
- Calculate opportunity score as: volume/(difficulty+1)

**Step 2: Get Keyword Data**
1.  Call \`get_keyword_volume\` with your keyword list.
2.  Call \`get_keyword_difficulty\` with the same list.
3.  Combine the data.

**Step 3: Analysis & Report**
1.  Calculate Priority Score: \`(Search Volume / (Keyword Difficulty + 1))\`
2.  Filter and sort by priority.
3.  Generate report based on the specified output format:

**For "summary" format:**
<KEYWORDS>
| Rank | Keyword | Volume | Difficulty | CPC | Priority Score |
|------|---------|--------|------------|-----|----------------|
| [Top 10 -15 keywords with data rows sorted by priority] |
</KEYWORDS>

<RECOMMENDATIONS>
## SEO Strategy Summary
• **Business Overview**: [Brief description]
• **Key Recommendations**: [3-5 bullet points]
• **Priority Actions**: [Immediate next steps]
</RECOMMENDATIONS>

**For "table" format:**
<KEYWORDS>
| Rank | Keyword | Volume | Difficulty | CPC | Priority Score |
|------|---------|--------|------------|-----|----------------|
| [Data rows sorted by priority] |
</KEYWORDS>

<ANALYSIS>
## Keyword Analysis Report
### Additional Metrics
[Include competition analysis, trend data if available]
</ANALYSIS>

**For "blog" format:**
<KEYWORDS>
| Rank | Keyword | Volume | Difficulty | CPC | Priority Score |
|------|---------|--------|------------|-----|----------------|
| [Data rows sorted by priority] |
</KEYWORDS>

<CONTENT>
# [Business Name] SEO Strategy: Complete Guide

## Introduction
[Engaging introduction about the business and SEO opportunity]

## Market Analysis
[Detailed market research findings]

## Keyword Opportunities
[Comprehensive keyword analysis with context]

## Content Strategy Recommendations
[Detailed content plan with examples]

## Implementation Roadmap
[Step-by-step action plan]

## Conclusion
[Summary and call-to-action]
</CONTENT>

**Keep it focused and efficient. Quality over quantity.**
`

// Function to get Supabase connection string with fallback to process.env
async function getSupabaseConnectionString(): Promise<string> {
  // Check if we're in a browser environment
  if (typeof window !== 'undefined') {
    throw new Error('PostgreSQL connection cannot be established in browser environment')
  }
  
  // Try multiple ways to get the environment variable
  let connectionString = env.SUPABASE_DB_URL || process.env.SUPABASE_DB_URL
  
  // In development, SvelteKit might not load env vars properly, try manual load
  if (!connectionString && typeof process !== 'undefined' && typeof require !== 'undefined') {
    try {
      // Try to read from different .env files in development
      if (process.env.NODE_ENV !== 'production') {
        const fs = require('fs')
        const path = require('path')
        
        // Try different env file locations
        const envFiles = [
          path.join(process.cwd(), '.env.local'),
          path.join(process.cwd(), '.env'),
          path.join(process.cwd(), '.env.development'),
          path.join(process.cwd(), '.env.development.local')
        ]
        
        for (const envFile of envFiles) {
          console.log('SEO Agent: Looking for .env file at:', envFile)
          if (fs.existsSync(envFile)) {
            console.log('SEO Agent: Found env file:', envFile) 
            const envContent = fs.readFileSync(envFile, 'utf8')
            console.log('SEO Agent: Env file content length:', envContent.length)
            
            // Try different patterns for SUPABASE_DB_URL
            const patterns = [
              /^SUPABASE_DB_URL=(.+)$/m,
              /^SUPABASE_DB_URL="(.+)"$/m,
              /^SUPABASE_DB_URL='(.+)'$/m
            ]
            
            for (const pattern of patterns) {
              const match = envContent.match(pattern)
              if (match) {
                connectionString = match[1].trim()
                console.log('🔧 SEO Agent: Loaded SUPABASE_DB_URL from', envFile, 'using pattern', pattern.source)
                break
              }
            }
            
            if (connectionString) break
          } else {
            console.log('SEO Agent: Env file does not exist:', envFile)
          }
        }
        
        if (!connectionString) {
          console.log('SEO Agent: SUPABASE_DB_URL not found in any env files')
        }
      }
    } catch (error) {
      console.warn('SEO Agent: Could not manually load .env file:', error.message)
    }
  }
  
  if (!connectionString) {
    throw new Error('SUPABASE_DB_URL environment variable is required. Please use the Direct Connection string from Supabase Dashboard.')
  }

  // Parse connection string to ensure it's valid
  try {
    const url = new URL(connectionString)
    
    // Validate that it's a PostgreSQL connection string
    if (url.protocol !== 'postgresql:' && url.protocol !== 'postgres:') {
      throw new Error('Connection string must use postgresql:// or postgres:// protocol')
    }
  } catch (error) {
    throw new Error(`Invalid SUPABASE_DB_URL format: ${error.message}`)
  }

  return connectionString
}

export async function createSEOStrategistAgent(llmConfig?: LLMConfig) {
  const defaultConfig = { provider: "openai", model: "gpt-4o-mini" }
  const finalConfig = llmConfig || defaultConfig

  const model = createLLMClient(finalConfig)

  let memory: Memory
  
  try {
    // Get the connection string for PostgresStore
    const connectionString = await getSupabaseConnectionString()
    
    // Create memory instance for conversation persistence
    memory = new Memory({
      storage: new PostgresStore({
        connectionString,
      }),
      options: {
        // Disable semantic recall for now (requires vector store)
        // semanticRecall: { 
        //   topK: 10, // Recall up to 10 relevant past messages
        //   messageRange: 5 // Consider recent 5 messages for context
        // },
        workingMemory: { enabled: true }, // Enable working memory for complex SEO tasks
      },
    })

    console.log("✅ SEO Agent: PostgreSQL memory initialized successfully")

  } catch (error) {
    console.error("=== SEO AGENT MEMORY FALLBACK ===")
    console.error("Failed to initialize PostgreSQL memory:", error.message)
    console.error("Falling back to in-memory storage for debugging...")
    console.error("===================================")
    
    // Fallback to basic memory without persistent storage
    memory = new Memory({
      options: {
        workingMemory: { enabled: true }, // Enable working memory for complex SEO tasks
      },
    })
  }

  return new Agent({
    name: "SEO Strategist",
    instructions: SEO_SYSTEM_PROMPT,
    model,
    memory, // Add memory to enable conversation context for SEO analysis
    tools: {
      webSearch: webSearchTool,
      get_keyword_volume: keywordVolumeTool,
      get_keyword_difficulty: keywordDifficultyTool,
      get_related_keywords: relatedKeywordsTool,
      get_domain_intersection: domainIntersectionTool,
      get_keywords_for_site: keywordsForSiteTool,
    },
  })
}

// Default instance - should use environment variables
const envConfig =
  env.LLM_PROVIDER && env.LLM_MODEL
    ? {
        provider: env.LLM_PROVIDER as "openai" | "anthropic" | "google",
        model: env.LLM_MODEL,
      }
    : undefined

console.log("=== DEFAULT SEO AGENT CREATION ===")
console.log("Environment config detected:", envConfig)
console.log("env.LLM_PROVIDER:", env.LLM_PROVIDER)
console.log("env.LLM_MODEL:", env.LLM_MODEL)

// Create default instance asynchronously
let _seoStrategistAgent: Agent | null = null

export const seoStrategistAgent = (() => {
  if (!_seoStrategistAgent) {
    // Initialize the agent asynchronously
    createSEOStrategistAgent(envConfig).then(agent => {
      _seoStrategistAgent = agent
      console.log("✅ SEO Strategist Agent initialized with memory")
    }).catch(error => {
      console.error("❌ Failed to initialize SEO Strategist Agent:", error)
    })
    
    // Return a temporary agent for immediate use (fallback)
    return new Agent({
      name: "SEO Strategist (Fallback)",
      instructions: SEO_SYSTEM_PROMPT,
      model: createLLMClient(envConfig || { provider: "openai", model: "gpt-4o-mini" }),
      tools: {
        webSearch: webSearchTool,
        get_keyword_volume: keywordVolumeTool,
        get_keyword_difficulty: keywordDifficultyTool,
        get_related_keywords: relatedKeywordsTool,
        get_domain_intersection: domainIntersectionTool,
        get_keywords_for_site: keywordsForSiteTool,
      },
    })
  }
  
  return _seoStrategistAgent
})()

// Export function to get the fully initialized agent
export async function getSEOStrategistAgent() {
  if (_seoStrategistAgent) {
    return _seoStrategistAgent
  }
  
  // If not initialized yet, create it now
  _seoStrategistAgent = await createSEOStrategistAgent(envConfig)
  return _seoStrategistAgent
}
