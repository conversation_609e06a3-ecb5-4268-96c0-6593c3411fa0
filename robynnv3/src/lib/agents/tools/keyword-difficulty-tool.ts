import { z } from 'zod'
import { createTool } from '@mastra/core'
import { env } from '$env/dynamic/private'

const keywordDifficultySchema = z.object({
  keywords: z.array(z.string()).max(1000).describe('Array of keywords to get difficulty scores for (max 1000)'),
  location_code: z.number().optional().default(2840).describe('Location code for search data (default: 2840 for United States)'),
  language_code: z.string().optional().default('en').describe('Language code for search data (default: en for English)')
})

export const keywordDifficultyTool = createTool({
  id: 'get_keyword_difficulty',
  description: 'Get SEO keyword difficulty scores (0-100) for a list of keywords using DataForSEO Labs API',
  inputSchema: keywordDifficultySchema,
  execute: async (context) => {
    const { keywords, location_code, language_code } = context.context
    
    const apiLogin = env.DATAFORSEO_LOGIN
    const apiPassword = env.DATAFORSEO_PASSWORD
    
    console.log('Keyword Difficulty Tool - Checking credentials:', { 
      hasLogin: !!apiLogin, 
      hasPassword: !!apiPassword,
      loginLength: apiLogin?.length || 0 
    })
    
    if (!apiLogin || !apiPassword) {
      console.error('DataForSEO credentials missing - returning mock data for testing')
      // Return mock data instead of throwing error
      const mockResults = keywords.map(keyword => ({
        keyword,
        keyword_difficulty: Math.floor(Math.random() * 80) + 10, // 10-90 range
        se_type: 'google'
      }))
      
      return {
        success: true,
        total_keywords: keywords.length,
        results_count: mockResults.length,
        cost: 0,
        location_code,
        language_code,
        results: mockResults,
        note: 'Mock data returned - DataForSEO credentials not configured'
      }
    }

    // Create base64 encoded credentials
    const credentials = Buffer.from(`${apiLogin}:${apiPassword}`).toString('base64')
    
    try {
      const requestData = [{
        location_code,
        language_code,
        keywords
      }]

      console.log('🎯 Making DataForSEO keyword difficulty request for', keywords.length, 'keywords')
      console.log('📝 Keywords:', keywords.slice(0, 5).join(', ') + (keywords.length > 5 ? '...' : ''))
      console.log('⏱️ Request start time:', new Date().toISOString())

      const controller = new AbortController()
      const startTime = Date.now()

      const timeoutId = setTimeout(() => {
        const elapsed = Date.now() - startTime
        console.log('⏰ Keyword difficulty tool timeout after', elapsed, 'ms')
        controller.abort()
      }, 15000) // 15 second timeout (reduced from 30s)

      const response = await fetch('https://api.dataforseo.com/v3/dataforseo_labs/google/bulk_keyword_difficulty/live', {
        method: 'POST',
        headers: {
          'Authorization': `Basic ${credentials}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData),
        signal: controller.signal
      })

      const requestTime = Date.now() - startTime
      console.log('📡 DataForSEO keyword difficulty response received in', requestTime, 'ms')
      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new Error(`DataForSEO API error: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()
      console.log('📊 DataForSEO keyword difficulty response status:', data.status_code, data.status_message)

      if (data.status_code !== 20000) {
        console.error('❌ DataForSEO API error:', data.status_message)
        throw new Error(`DataForSEO API error: ${data.status_message}`)
      }

      // Extract and format the results
      const results = []
      if (data.tasks && data.tasks[0] && data.tasks[0].result && data.tasks[0].result[0] && data.tasks[0].result[0].items) {
        console.log('🔍 Processing', data.tasks[0].result[0].items.length, 'keyword difficulty results')
        for (const item of data.tasks[0].result[0].items) {
          results.push({
            keyword: item.keyword,
            keyword_difficulty: item.keyword_difficulty || 0,
            se_type: item.se_type
          })
        }
      } else {
        console.warn('⚠️ No keyword difficulty results found in response structure')
      }

      const totalTime = Date.now() - startTime
      console.log('✅ Keyword difficulty tool completed successfully in', totalTime, 'ms, returning', results.length, 'results')

      return {
        success: true,
        total_keywords: keywords.length,
        results_count: results.length,
        cost: data.cost || 0,
        location_code,
        language_code,
        results
      }
    } catch (error) {
      const totalTime = Date.now() - startTime
      console.error('❌ Keyword difficulty API error after', totalTime, 'ms:', error)

      if (error.name === 'AbortError') {
        console.error('🚫 Request was aborted due to timeout')
      }

      throw new Error(`Failed to get keyword difficulty data: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
})
