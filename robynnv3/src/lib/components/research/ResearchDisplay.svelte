<script lang="ts">
  import { fade, fly } from 'svelte/transition'
  import { quintOut } from 'svelte/easing'
  import {
    Download,
    Share2,
    Bookmark,
    Clock,
    CheckCircle2,
    AlertTriangle,
    Send
  } from 'lucide-svelte'

  import CompanyOverviewCard from './CompanyOverviewCard.svelte'
  import CompetitiveAnalysisCard from './CompetitiveAnalysisCard.svelte'
  import ContactsCard from './ContactsCard.svelte'
  import IntelligenceInsightsCard from './IntelligenceInsightsCard.svelte'
  import WebPresenceCard from './WebPresenceCard.svelte'
  import TechnicalAnalysisCard from './TechnicalAnalysisCard.svelte'
  import PublishingModal from '../publishing/PublishingModal.svelte'

  export let researchData: any = {}

  // Publishing modal state
  let showPublishingModal = false

  // Extract data sections
  $: targetCompany = researchData.targetCompany || {}
  $: competitors = researchData.competitors || []
  $: intelligence = researchData.intelligence || {}
  $: actionableInsights = researchData.actionable_insights || {}
  $: metadata = researchData.metadata || {}

  // Enhanced data extraction for Firecrawl integration
  // Check both new nested structure (targetCompany.webPresence) and legacy structure (researchData.web_presence)
  $: webPresence = targetCompany.webPresence || researchData.web_presence || {}
  $: technicalAnalysis = targetCompany.technicalAnalysis || researchData.technical_analysis || {}

  // Extract market intelligence from enhanced structure
  $: marketIntelligence = researchData.marketIntelligence || {}

  // Get overall confidence color
  function getConfidenceColor(score: number): string {
    if (score >= 0.9) return 'text-green-600'
    if (score >= 0.7) return 'text-yellow-600'
    return 'text-red-600'
  }

  // Format confidence score as percentage
  function formatConfidence(score: number): string {
    return `${Math.round(score * 100)}%`
  }

  // Format date for display
  function formatDate(dateString: string): string {
    if (!dateString) return 'Unknown'
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    } catch {
      return 'Unknown'
    }
  }

  // Get data quality color
  function getDataQualityColor(quality: string): string {
    switch (quality?.toLowerCase()) {
      case 'high': return 'text-green-600'
      case 'medium': return 'text-yellow-600'
      case 'low': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }

  // Enhanced export with Firecrawl data (exports as JSON)
  function exportToJSON() {
    // Enhanced export data including Firecrawl insights
    const exportData = {
      // Basic company information
      company: targetCompany.name,
      domain: targetCompany.domain,
      industry: targetCompany.industry,
      employees: targetCompany.employees,
      location: targetCompany.location,

      // Enhanced web presence data
      webPresence: Object.keys(webPresence).length > 0 ? {
        landingPageAnalysis: webPresence.landing_page_analysis || {},
        contentStrategy: webPresence.content_strategy || {},
        marketPositioning: webPresence.market_positioning || {}
      } : null,

      // Technical analysis data
      technicalAnalysis: Object.keys(technicalAnalysis).length > 0 ? {
        technologyStack: technicalAnalysis.technology_stack || {},
        performanceMetrics: technicalAnalysis.performance_metrics || {},
        securityAnalysis: technicalAnalysis.security_analysis || {}
      } : null,

      // Competitive intelligence
      competitors: competitors.map((comp: any) => ({
        name: comp.name,
        domain: comp.domain,
        industry: comp.industry,
        employees: comp.employees,
        confidenceScore: comp.confidence_score
      })),

      // Contact information
      contacts: metadata.totalContacts,

      // Data sources and confidence
      dataSources: targetCompany.data_sources || ['Apollo API', 'Exa Search'],
      overallConfidence: targetCompany.confidence_score || metadata.dataQuality,

      // Export metadata
      exportDate: new Date().toISOString(),
      exportVersion: '2.0-firecrawl-enhanced'
    }

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `research-enhanced-${targetCompany.name || 'company'}-${new Date().toISOString().split('T')[0]}.json`
    a.click()
    URL.revokeObjectURL(url)
  }

  // Share research (placeholder)
  function shareResearch() {
    if (navigator.share) {
      navigator.share({
        title: `Research Report: ${targetCompany.name}`,
        text: `Company research report for ${targetCompany.name} with ${competitors.length} competitors and ${metadata.totalContacts} contacts.`,
        url: window.location.href
      })
    } else {
      // Fallback: copy link to clipboard
      navigator.clipboard.writeText(window.location.href)
    }
  }

  // Bookmark research (placeholder)
  function bookmarkResearch() {
    // This would integrate with a bookmarking system
    console.log('Bookmark functionality would be implemented here')
  }

  // Open publishing modal
  function openPublishingModal() {
    showPublishingModal = true
  }
</script>

{#if Object.keys(researchData).length > 0}
  <div 
    class="space-y-8"
    in:fade={{ duration: 600, easing: quintOut }}
  >
    <!-- Research Header -->
    <div 
      class="card-professional p-6"
      in:fly={{ y: -20, duration: 400, easing: quintOut }}
    >
      <div class="flex items-start justify-between">
        <div class="flex-1">
          <h1 class="text-2xl font-bold text-foreground mb-2">
            Research Report: {targetCompany.name || 'Company Analysis'}
          </h1>
          <div class="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
            {#if metadata.data_freshness}
              <div class="flex items-center gap-1">
                <Clock class="w-4 h-4" />
                <span>Generated {formatDate(metadata.data_freshness)}</span>
              </div>
            {/if}
            
            {#if metadata.confidence_score}
              <div class="flex items-center gap-1">
                <CheckCircle2 class="w-4 h-4 {getConfidenceColor(metadata.confidence_score)}" />
                <span class="{getConfidenceColor(metadata.confidence_score)}">
                  {formatConfidence(metadata.confidence_score)} confidence
                </span>
              </div>
            {/if}
            
            {#if metadata.dataQuality}
              <div class="flex items-center gap-1">
                <AlertTriangle class="w-4 h-4 {getDataQualityColor(metadata.dataQuality)}" />
                <span class="{getDataQualityColor(metadata.dataQuality)}">
                  {metadata.dataQuality} quality
                </span>
              </div>
            {/if}
            
            {#if metadata.processingTime}
              <span>Processed in {metadata.processingTime}</span>
            {/if}
          </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="flex items-center gap-2">
          <button
            on:click={bookmarkResearch}
            class="inline-flex items-center gap-2 px-3 py-2 text-sm text-muted-foreground hover:text-foreground border border-border rounded-md hover:bg-secondary transition-colors"
            title="Bookmark this research"
          >
            <Bookmark class="w-4 h-4" />
            Save
          </button>

          <button
            on:click={shareResearch}
            class="inline-flex items-center gap-2 px-3 py-2 text-sm text-muted-foreground hover:text-foreground border border-border rounded-md hover:bg-secondary transition-colors"
            title="Share this research"
          >
            <Share2 class="w-4 h-4" />
            Share
          </button>

          <button
            on:click={openPublishingModal}
            class="inline-flex items-center gap-2 px-3 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            title="Publish research results to configured destinations"
          >
            <Send class="w-4 h-4" />
            Publish
          </button>

          <button
            on:click={exportToJSON}
            class="inline-flex items-center gap-2 px-3 py-2 text-sm bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
            title="Export as JSON"
          >
            <Download class="w-4 h-4" />
            Export JSON
          </button>
        </div>
      </div>
      
      <!-- Quick Stats -->
      {#if metadata.totalCompanies || metadata.totalContacts}
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6 pt-6 border-t border-border">
          {#if metadata.totalCompanies}
            <div class="text-center">
              <div class="text-2xl font-bold text-foreground">{metadata.totalCompanies}</div>
              <div class="text-xs text-muted-foreground">Companies Analyzed</div>
            </div>
          {/if}
          
          {#if metadata.totalContacts}
            <div class="text-center">
              <div class="text-2xl font-bold text-foreground">{metadata.totalContacts}</div>
              <div class="text-xs text-muted-foreground">Contacts Found</div>
            </div>
          {/if}
          
          {#if competitors.length}
            <div class="text-center">
              <div class="text-2xl font-bold text-foreground">{competitors.length}</div>
              <div class="text-xs text-muted-foreground">Competitors</div>
            </div>
          {/if}
          
          {#if metadata.sources_used}
            <div class="text-center">
              <div class="text-2xl font-bold text-foreground">{metadata.sources_used.length}</div>
              <div class="text-xs text-muted-foreground">Data Sources</div>
            </div>
          {/if}
        </div>
      {/if}
    </div>

    <!-- Company Overview -->
    {#if Object.keys(targetCompany).length > 0}
      <div in:fly={{ y: 20, duration: 400, delay: 100, easing: quintOut }}>
        <CompanyOverviewCard company={targetCompany} />
      </div>
    {/if}

    <!-- Competitive Analysis -->
    {#if competitors.length > 0}
      <div in:fly={{ y: 20, duration: 400, delay: 200, easing: quintOut }}>
        <CompetitiveAnalysisCard {competitors} {targetCompany} />
      </div>
    {/if}

    <!-- Intelligence Insights -->
    {#if Object.keys(intelligence).length > 0 || Object.keys(actionableInsights).length > 0}
      <div in:fly={{ y: 20, duration: 400, delay: 300, easing: quintOut }}>
        <IntelligenceInsightsCard {intelligence} {actionableInsights} />
      </div>
    {/if}

    <!-- Web Presence Analysis -->
    {#if Object.keys(webPresence).length > 0}
      <div in:fly={{ y: 20, duration: 400, delay: 350, easing: quintOut }}>
        <WebPresenceCard {webPresence} companyName={targetCompany.name || 'Company'} />
      </div>
    {/if}

    <!-- Technical Analysis -->
    {#if Object.keys(technicalAnalysis).length > 0}
      <div in:fly={{ y: 20, duration: 400, delay: 375, easing: quintOut }}>
        <TechnicalAnalysisCard technicalData={technicalAnalysis} companyName={targetCompany.name || 'Company'} />
      </div>
    {/if}

    <!-- Contacts -->
    {#if (targetCompany.contacts && targetCompany.contacts.length > 0) || competitors.some((c: any) => c.contacts && c.contacts.length > 0)}
      <div in:fly={{ y: 20, duration: 400, delay: 425, easing: quintOut }}>
        <ContactsCard {targetCompany} {competitors} />
      </div>
    {/if}

    <!-- Data Attribution Footer -->
    <div
      class="card-professional p-4 text-center"
      in:fly={{ y: 20, duration: 400, delay: 475, easing: quintOut }}
    >
      <div class="text-xs text-muted-foreground space-y-1">
        <p>
          Research powered by {metadata.sources_used ? metadata.sources_used.join(' • ') : 'Apollo API • Exa Search • Firecrawl'}
        </p>
        {#if metadata.api_calls_made}
          <p>
            API calls: {Object.entries(metadata.api_calls_made).map(([key, value]) => `${key}: ${value}`).join(' • ')}
          </p>
        {/if}
        <p class="text-xs">
          Generated on {formatDate(metadata.data_freshness || new Date().toISOString())} • 
          Data accuracy subject to source reliability
        </p>
      </div>
    </div>
  </div>
{:else}
  <!-- No Data State -->
  <div class="text-center py-12 text-muted-foreground">
    <div class="w-16 h-16 mx-auto mb-4 bg-secondary rounded-full flex items-center justify-center">
      <AlertTriangle class="w-8 h-8" />
    </div>
    <h3 class="text-lg font-semibold mb-2">No Research Data Available</h3>
    <p class="text-sm">Start a research query to see comprehensive company analysis here.</p>
  </div>
{/if}

<!-- Publishing Modal -->
<PublishingModal
  bind:isOpen={showPublishingModal}
  {researchData}
  title="Publish Research Results"
  on:close={() => showPublishingModal = false}
/>
