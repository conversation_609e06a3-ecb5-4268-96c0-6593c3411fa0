<script lang="ts">
  console.log('🔥 AGUI CANARY BUILD:', Date.now(), 'FIXES APPLIED v2.0')
  
  import { writable } from 'svelte/store'
  import { createEventDispatcher, tick } from 'svelte'
  import {
    Bot,
    User,
    Send,
    Loader2,
    X,
    Zap,
    CheckCircle2,
    Search,
    Globe,
    Building2,
    Plus,
    Crosshair,
    UserPlus,
    File,
    Download,
    Edit3,
    Book,
    XCircle,
    Clock,
    Pause,
    Play,
    Square
  } from 'lucide-svelte'
  import { slide, fade, fly, scale } from 'svelte/transition'
  import { quintOut, backOut } from 'svelte/easing'
  import ToolBadge from '../ToolBadge.svelte'
  import { ToolRegistry } from '$lib/services/tool-registry'
  import { conversationStore, sendMessage, addMessageToCanvas, cancelRun, pauseRun, resumeRun, isLoading, isPaused, canCancel, canPause } from '$lib/agui/conversation-store'

  let { isOpen = $bindable(false) } = $props()
  
  const dispatch = createEventDispatcher()

  interface AGUIMessage {
    id: string
    role: 'user' | 'assistant'
    content: string
    timestamp: Date
    status?: 'draft' | 'streaming' | 'complete'
    progress?: {
      current: number
      total: number
      currentTask?: string
    }
  }

  let input = $state('')
  let messagesContainer = $state<HTMLElement>()

  // Complete tools list for Product Marketing Agent (matching actual Mastra tools)
  const aguiTools = [
    // Search & Discovery Tools
    { name: 'web_search', description: 'Web Search', icon: Search, category: 'research' },
    { name: 'exa_search', description: 'Enhanced Search', icon: Zap, category: 'research' },
    { name: 'firecrawl_search', description: 'Deep Search', icon: Globe, category: 'research' },
    
    // Company Intelligence Tools  
    { name: 'apollo_search_company', description: 'Company Intel', icon: Building2, category: 'business' },
    { name: 'apollo_find_companies', description: 'Company Discovery', icon: Crosshair, category: 'business' },
    { name: 'apollo_find_contacts', description: 'Contact Finder', icon: UserPlus, category: 'business' },
    
    // Content Analysis Tools
    { name: 'firecrawl_scrape', description: 'Page Scraper', icon: File, category: 'analysis' },
    { name: 'firecrawl_extract', description: 'Data Extractor', icon: Download, category: 'analysis' },
    { name: 'content_generation', description: 'Content Gen', icon: Edit3, category: 'creation' },
    { name: 'text_summarization', description: 'Summarizer', icon: Book, category: 'analysis' }
  ]

  // Subscribe to conversation store
  let messages = $derived($conversationStore.messages)
  let toolCalls = $derived($conversationStore.toolCalls)
  
  // Function to check if a message is complete (not currently being streamed)
  function isMessageComplete(message: AGUIMessage): boolean {
    // Check new status field first if available
    if (message.status) {
      return message.status === 'complete' && message.content && message.content.trim().length > 10
    }
    
    // Fallback to old logic for backward compatibility
    const hasSubstantialContent = message.content && message.content.trim().length > 10
    const isLastMessage = messages[messages.length - 1]?.id === message.id
    
    if (!isLastMessage) {
      return hasSubstantialContent
    }
    
    return !$isLoading && hasSubstantialContent
  }

  // Optimized ID generation using crypto.randomUUID when available
  function generateId(): string {
    if (typeof crypto !== 'undefined' && crypto.randomUUID) {
      return crypto.randomUUID()
    }
    return Math.random().toString(36).substring(2, 11)
  }

  async function handleSendMessage(messageText?: string) {
    const userMessage = messageText || input.trim()
    if (!userMessage || $isLoading) return

    input = ''
    
    // Use AG-UI conversation manager to send message
    try {
      const endpoint = `${window.location.pathname}?stream=true`
      await sendMessage(endpoint, userMessage, {})
    } catch (error) {
      console.error('Failed to send AG-UI message:', error)
    }
  }

  function handleKeyDown(event: KeyboardEvent) {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault()
      handleSendMessage()
    }
    // Escape key to close sidebar
    if (event.key === 'Escape') {
      closeSidebar()
    }
  }

  // Debounced scroll optimization
  let scrollTimeout: ReturnType<typeof setTimeout>
  function optimizedScroll() {
    if (scrollTimeout) clearTimeout(scrollTimeout)
    scrollTimeout = setTimeout(() => {
      if (messagesContainer) {
        messagesContainer.scrollTop = messagesContainer.scrollHeight
      }
    }, 16) // ~60fps
  }

  function closeSidebar() {
    isOpen = false
    dispatch('close')
  }

  function formatContent(content: string): string {
    // Check if content looks like JSON
    const trimmedContent = content.trim()
    if (trimmedContent.startsWith('{') && trimmedContent.endsWith('}')) {
      try {
        const jsonData = JSON.parse(trimmedContent)
        
        // If it's structured research data, format it nicely
        if (jsonData.targetCompany || jsonData.metadata || jsonData.companies || jsonData.recommendations) {
          let formatted = ''
          
          if (jsonData.targetCompany) {
            formatted += `<strong>Company:</strong> ${jsonData.targetCompany.name}<br>`
            if (jsonData.targetCompany.description) {
              formatted += `<em>${jsonData.targetCompany.description}</em><br><br>`
            }
          }
          
          if (jsonData.metadata) {
            formatted += `<strong>Analysis Confidence:</strong> ${Math.round((jsonData.metadata.confidence_score || 0) * 100)}%<br>`
            if (jsonData.metadata.processing_time) {
              formatted += `<strong>Processing Time:</strong> ${jsonData.metadata.processing_time}<br><br>`
            }
          }
          
          if (jsonData.companies && Array.isArray(jsonData.companies)) {
            formatted += `<strong>Companies Analyzed:</strong> ${jsonData.companies.length}<br>`
            jsonData.companies.slice(0, 3).forEach((company: any) => {
              if (company.name) {
                formatted += `• ${company.name}`
                if (company.confidence_score) {
                  formatted += ` (${Math.round(company.confidence_score * 100)}% confidence)`
                }
                formatted += '<br>'
              }
            })
          }
          
          if (jsonData.recommendations && Array.isArray(jsonData.recommendations)) {
            formatted += '<br><strong>Recommendations:</strong><br>'
            jsonData.recommendations.forEach((rec: any) => {
              if (rec.title) {
                formatted += `<strong>• ${rec.title}</strong><br>`
                if (rec.content_angle) {
                  formatted += `<em>Focus: ${rec.content_angle}</em><br>`
                }
                if (rec.target_keywords && Array.isArray(rec.target_keywords)) {
                  formatted += `Keywords: ${rec.target_keywords.join(', ')}<br>`
                }
                if (rec.priority_score) {
                  formatted += `Priority: ${rec.priority_score}/10<br>`
                }
                formatted += '<br>'
              }
            })
          }
          
          if (jsonData.market_analysis) {
            formatted += '<br><strong>Market Analysis:</strong><br>'
            if (jsonData.market_analysis.competitive_landscape) {
              const landscape = jsonData.market_analysis.competitive_landscape
              formatted += `• Competitors: ${landscape.total_competitors || 'N/A'}<br>`
              formatted += `• Market Dominance: ${Math.round((landscape.avg_dominance || 0) * 100)}%<br>`
            }
          }
          
          return formatted
        }
        
        // For other JSON, don't display it - return empty string to hide JSON output
        return ''
      } catch (e) {
        // Not valid JSON, fall through to regular formatting
      }
    }
    
    // Basic HTML formatting for regular text
    return content
      .replace(/\n/g, '<br>')
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
  }

  // Export function to populate input field from example prompts
  export function populateInput(message: string) {
    input = message
    // Focus the input field for better UX
    const inputElement = document.getElementById('agui-message-input') as HTMLTextAreaElement
    if (inputElement) {
      inputElement.focus()
      // Move cursor to end of text
      inputElement.setSelectionRange(message.length, message.length)
    }
  }

  // Auto-scroll when messages change
  $effect(() => {
    if (messages.length > 0) {
      tick().then(() => optimizedScroll())
    }
  })
</script>

<style>
  .sidebar-container {
    width: 0;
    min-width: 0;
    overflow: hidden;
  }
  
  .sidebar-open {
    width: 400px;
    min-width: 400px;
  }
  
  .sidebar-closed {
    width: 0;
    min-width: 0;
  }

  @media (max-width: 768px) {
    .sidebar-open {
      position: fixed;
      top: 0;
      right: 0;
      height: 100vh;
      z-index: 50;
      width: 100vw;
      min-width: 100vw;
    }
  }

  /* Enhanced Animations */
  .progress-pulse {
    animation: progressPulse 2s ease-in-out infinite;
  }

  @keyframes progressPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
  }

  .task-enter {
    animation: taskEnter 0.4s ease-out;
  }

  @keyframes taskEnter {
    from {
      opacity: 0;
      transform: translateX(-10px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateX(0) scale(1);
    }
  }

  .control-button {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .control-button:hover {
    transform: scale(1.05);
  }

  .control-button:active {
    transform: scale(0.95);
  }

  /* Status indicator animations */
  .status-dot {
    animation: statusPulse 2s ease-in-out infinite;
  }

  @keyframes statusPulse {
    0%, 100% { 
      opacity: 1; 
      transform: scale(1);
    }
    50% { 
      opacity: 0.6; 
      transform: scale(1.1);
    }
  }
</style>

<!-- Sidebar Container - Responsive positioning based on screen size -->
<div
  class="sidebar-container flex flex-col bg-card border-l border-border shadow-lg transition-all duration-400 ease-out h-full"
  class:sidebar-open={isOpen}
  class:sidebar-closed={!isOpen}
  role="complementary"
  aria-label="AG-UI Research Assistant Chat Sidebar"
  aria-live="polite"
>
  {#if isOpen}
    <!-- Header -->
    <header class="flex items-center justify-between p-4 border-b border-border bg-card">
      <div class="flex items-center gap-2">
        <Zap class="w-5 h-5 text-primary" aria-hidden="true" />
        <h2 class="linear-heading text-lg font-semibold" id="sidebar-title">Product Marketing Agent</h2>
      </div>
      <button
        onclick={closeSidebar}
        class="p-1 hover:bg-muted rounded transition-colors"
        aria-label="Close AG-UI Assistant sidebar"
      >
        <X class="w-4 h-4" />
      </button>
    </header>

    <!-- Tool Access Badges -->
    <section class="px-3 sm:px-4 py-2 border-b border-border" aria-labelledby="tools-heading">
      <h3
        id="tools-heading"
        class="text-xs text-muted-foreground mb-2"
        in:fade={{ duration: 300, delay: 200 }}
      >
        Available Research Tools
      </h3>
      <div
        class="flex flex-wrap gap-1 sm:gap-1.5"
        role="list"
        aria-label="Available research tools"
      >
        {#each aguiTools as tool, i}
          {@const IconComponent = tool.icon}
          <div
            in:scale={{ duration: 300, delay: 300 + (i * 100), easing: backOut }}
            class="transform hover:scale-105 transition-transform duration-200 flex-shrink-0"
            role="listitem"
          >
            <div class="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium bg-muted rounded-md">
              <IconComponent class="w-3 h-3" />
              {tool.description}
            </div>
          </div>
        {/each}
      </div>
    </section>

    <!-- Messages -->
    <main
      bind:this={messagesContainer}
      class="flex-1 overflow-y-auto p-3 sm:p-4 space-y-3 sm:space-y-4"
      role="log"
      aria-label="Chat conversation"
      aria-live="polite"
      aria-atomic="false"
    >
      {#each messages as message}
        <!-- Only show messages with actual content or that are currently streaming -->
        {#if message.content.trim() || message.status === 'streaming'}
          <div
            class="flex gap-3 {message.role === 'user' ? 'flex-row-reverse' : ''}"
            in:fly={{
              y: message.role === 'user' ? -20 : 20,
              duration: 400,
              easing: quintOut
            }}
          >
            <div class="w-8 h-8 flex-shrink-0 flex items-center justify-center rounded-full border-2 border-border bg-{message.role === 'user' ? 'primary' : 'secondary'}">
              {#if message.role === 'user'}
                <User class="w-4 h-4 text-primary-foreground" />
              {:else}
                <Bot class="w-4 h-4 text-secondary-foreground" />
              {/if}
            </div>
            <div class="flex-1 {message.role === 'user' ? 'text-right' : ''}">
              <div
                class="inline-block max-w-full p-3 rounded-lg {message.role === 'user' ? 'bg-primary text-primary-foreground' : 'bg-muted'} transform transition-all duration-200 hover:shadow-md"
                in:scale={{ duration: 300, delay: 200, easing: quintOut }}
              >
              {#if message.role === 'assistant'}
                <!-- Enhanced Progress indicator for streaming messages -->
                {#if message.status === 'streaming' && message.progress}
                  <div class="mb-3 p-3 bg-muted/50 rounded-lg border border-border/50">
                    <!-- Current Task Header -->
                    <div class="flex items-center gap-2 mb-3">
                      <Loader2 class="w-4 h-4 animate-spin text-primary" />
                      <span class="text-sm font-medium">
                        {message.progress.currentTask || 'Processing...'}
                      </span>
                      {#if message.progress.estimatedTimeRemaining}
                        <span class="text-xs text-muted-foreground ml-auto">
                          ~{Math.ceil(message.progress.estimatedTimeRemaining / 1000)}s remaining
                        </span>
                      {/if}
                    </div>

                    <!-- Main Progress Bar -->
                    <div class="mb-3">
                      <div class="flex justify-between text-xs text-muted-foreground mb-1">
                        <span>Overall Progress</span>
                        <span>{message.progress.current} of {message.progress.total}</span>
                      </div>
                      <div class="w-full bg-muted/70 rounded-full h-2 overflow-hidden">
                        <div 
                          class="bg-gradient-to-r from-primary to-primary/80 h-2 rounded-full transition-all duration-700 ease-out shadow-sm"
                          style="width: {Math.min((message.progress.current / message.progress.total) * 100, 100)}%"
                        ></div>
                      </div>
                    </div>

                    <!-- Step Progress (if available) -->
                    {#if message.progress.stepProgress}
                      <div class="border-t border-border/30 pt-2">
                        <div class="flex justify-between text-xs text-muted-foreground mb-1">
                          <span class="font-medium">{message.progress.stepProgress.stepName}</span>
                          <span>Step {message.progress.stepProgress.step} of {message.progress.stepProgress.totalSteps}</span>
                        </div>
                        {#if message.progress.stepProgress.stepDescription}
                          <div class="text-xs text-muted-foreground mb-2">
                            {message.progress.stepProgress.stepDescription}
                          </div>
                        {/if}
                        <div class="w-full bg-muted/50 rounded-full h-1.5 overflow-hidden">
                          <div 
                            class="bg-gradient-to-r from-secondary to-secondary/80 h-1.5 rounded-full transition-all duration-500 ease-out"
                            style="width: {(message.progress.stepProgress.step / message.progress.stepProgress.totalSteps) * 100}%"
                          ></div>
                        </div>
                      </div>
                    {/if}

                    <!-- Task List (if available) -->
                    {#if message.tasks && message.tasks.length > 0}
                      <div class="border-t border-border/30 pt-2 mt-2">
                        <div class="text-xs font-medium text-muted-foreground mb-2">
                          Active Tasks:
                        </div>
                        <div class="space-y-1 max-h-20 overflow-y-auto">
                          {#each message.tasks.slice(-3) as task, i}
                            <div 
                              class="flex items-center gap-2 text-xs task-enter" 
                              style="animation-delay: {i * 0.1}s"
                            >
                              {#if task.status === 'running'}
                                <Loader2 class="w-3 h-3 animate-spin text-primary flex-shrink-0" />
                              {:else if task.status === 'success'}
                                <CheckCircle2 class="w-3 h-3 text-green-500 flex-shrink-0" />
                              {:else if task.status === 'error'}
                                <XCircle class="w-3 h-3 text-red-500 flex-shrink-0" />
                              {:else}
                                <Clock class="w-3 h-3 text-muted-foreground flex-shrink-0" />
                              {/if}
                              <span class="truncate" title={task.description || task.name}>
                                {task.name}
                              </span>
                              {#if task.priority}
                                <span class="px-1 py-0.5 rounded text-xs font-medium flex-shrink-0
                                  {task.priority === 'high' ? 'bg-red-100 text-red-700' : 
                                   task.priority === 'medium' ? 'bg-yellow-100 text-yellow-700' : 
                                   'bg-gray-100 text-gray-700'}">
                                  {task.priority}
                                </span>
                              {/if}
                            </div>
                          {/each}
                        </div>
                      </div>
                    {/if}
                  </div>
                {/if}
                
                <div class="prose prose-sm max-w-none" role="region" aria-label="Assistant response">
                  {@html formatContent(message.content)}
                </div>
                <!-- Add to Canvas Button - Only show when message is complete -->
                {#if isMessageComplete(message)}
                  <div class="mt-2 pt-2 border-t border-border/30">
                    <button
                      onclick={async (event) => {
                        console.log('=== ADD TO CANVAS BUTTON CLICKED ===')
                        console.log('Message ID:', message.id)
                        console.log('Message content type:', typeof message.content)
                        try {
                          const button = event.target
                          const originalText = button.textContent
                          
                          // Show loading state
                          button.textContent = 'Saving...'
                          button.disabled = true
                          
                          const result = await addMessageToCanvas(message.content, `Agent Response`)
                          console.log(`Successfully added ${result} to canvas`)
                          
                          // Show success feedback
                          button.textContent = '✓ Added!'
                          button.classList.add('bg-green-500/20', 'text-green-600')
                          setTimeout(() => {
                            button.textContent = originalText
                            button.disabled = false
                            button.classList.remove('bg-green-500/20', 'text-green-600')
                          }, 2000)
                        } catch (error) {
                          console.error('Failed to add to canvas:', error)
                          
                          // Show error feedback
                          const button = event.target
                          const originalText = button.textContent
                          button.textContent = '✗ Error'
                          button.classList.add('bg-red-500/20', 'text-red-600')
                          setTimeout(() => {
                            button.textContent = originalText
                            button.disabled = false
                            button.classList.remove('bg-red-500/20', 'text-red-600')
                          }, 2000)
                        }
                      }}
                      class="inline-flex items-center gap-1 px-2 py-1 text-xs bg-primary/10 hover:bg-primary/20 text-primary rounded transition-colors"
                      title="Add this response to canvas"
                    >
                      <Plus class="w-3 h-3" />
                      Add to Canvas
                    </button>
                  </div>
                {/if}
              {:else}
                <p class="text-sm">{message.content}</p>
              {/if}
            </div>
            <div
              class="text-xs text-muted-foreground mt-1 {message.role === 'user' ? 'text-right' : ''}"
              in:fade={{ duration: 300, delay: 400 }}
              aria-label="Message sent at {message.timestamp.toLocaleTimeString()}"
            >
              {message.timestamp.toLocaleTimeString()}
            </div>
          </div>
        </div>
        {/if}
      {/each}

      <!-- Tool Calls Display -->
      {#if toolCalls.length > 0}
        <div
          class="bg-card border border-border rounded-lg p-4 shadow-sm"
          in:slide={{ duration: 300, delay: 200, axis: 'y' }}
          role="status"
          aria-label="Tool execution progress"
        >
          <div class="flex items-center gap-2 mb-3">
            <div class="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
            <h4 class="font-medium text-sm">Tool Execution</h4>
          </div>

          <div class="space-y-2">
            {#each toolCalls as toolCall, i}
              <div
                class="flex items-center gap-3 p-3 rounded-md border border-border bg-card/50 transition-all duration-200 hover:bg-card"
                in:fly={{ x: -20, duration: 300, delay: 200 + (i * 100), easing: quintOut }}
              >
                <div class="flex-shrink-0">
                  {#if toolCall.status === 'completed'}
                    <div in:scale={{ duration: 400, easing: backOut }}>
                      <CheckCircle2
                        class="w-4 h-4 text-green-500 transition-all duration-300"
                        aria-label="Tool completed"
                      />
                    </div>
                  {:else if toolCall.status === 'pending'}
                    <div in:scale={{ duration: 300, easing: backOut }}>
                      <Loader2
                        class="w-4 h-4 animate-spin text-primary"
                        aria-label="Tool in progress"
                      />
                    </div>
                  {:else}
                    <div
                      class="w-4 h-4 border-2 border-red-500 rounded-full"
                      aria-label="Tool error"
                    ></div>
                  {/if}
                </div>
                
                <div class="flex-1 min-w-0">
                  <div class="font-medium text-sm {toolCall.status === 'completed' ? 'text-muted-foreground' : 'text-foreground'}">
                    {toolCall.name}
                  </div>
                  {#if toolCall.error}
                    <div class="text-xs text-red-500 mt-1 truncate">
                      Error: {toolCall.error}
                    </div>
                  {:else if Object.keys(toolCall.args).length > 0}
                    <div class="text-xs text-muted-foreground mt-1 truncate">
                      {JSON.stringify(toolCall.args).substring(0, 50)}...
                    </div>
                  {/if}
                </div>
              </div>
            {/each}
          </div>
        </div>
      {/if}

      <!-- Loading indicator -->
      {#if $isLoading && toolCalls.length === 0}
        <div
          class="bg-card border border-border rounded-lg p-4 shadow-sm"
          in:slide={{ duration: 300, delay: 200, axis: 'y' }}
          role="status"
          aria-label="Processing request"
        >
          <div class="flex items-center gap-2">
            <Loader2 class="w-4 h-4 animate-spin text-primary" />
            <span class="text-sm text-muted-foreground">Processing your request...</span>
          </div>
        </div>
      {/if}
    </main>

    <!-- Input -->
    <footer
      class="p-3 sm:p-4 border-t border-border bg-card sticky bottom-0 z-10"
      in:slide={{ duration: 300, delay: 400, axis: 'y' }}
    >
      <!-- Control Bar (when stream is active) -->
      {#if $isLoading || $canCancel || $canPause}
        <div class="flex items-center justify-between mb-3 p-2 bg-muted/50 rounded-lg border border-border/50">
          <div class="flex items-center gap-2">
            <div class="w-2 h-2 bg-primary rounded-full status-dot"></div>
            <span class="text-xs font-medium">
              {#if $isPaused}
                Paused
              {:else if $isLoading}
                Processing...
              {:else}
                Ready
              {/if}
            </span>
          </div>
          <div class="flex items-center gap-1">
            <!-- Pause/Resume Button -->
            {#if $canPause && !$isPaused}
              <button
                onclick={() => pauseRun()}
                class="control-button inline-flex items-center justify-center rounded p-1.5 text-xs font-medium transition-colors hover:bg-muted focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring"
                title="Pause processing"
                aria-label="Pause processing"
              >
                <Pause class="w-3 h-3" />
              </button>
            {/if}
            
            {#if $isPaused}
              <button
                onclick={() => resumeRun()}
                class="control-button inline-flex items-center justify-center rounded p-1.5 text-xs font-medium transition-colors hover:bg-muted focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring text-green-600"
                title="Resume processing"
                aria-label="Resume processing"
              >
                <Play class="w-3 h-3" />
              </button>
            {/if}
            
            <!-- Cancel Button -->
            {#if $canCancel}
              <button
                onclick={() => cancelRun('User requested cancellation')}
                class="control-button inline-flex items-center justify-center rounded p-1.5 text-xs font-medium transition-colors hover:bg-red-100 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring text-red-600"
                title="Cancel processing"
                aria-label="Cancel processing"
              >
                <Square class="w-3 h-3" />
              </button>
            {/if}
          </div>
        </div>
      {/if}

      <form onsubmit={(e) => { e.preventDefault(); handleSendMessage(); }} class="flex gap-2">
        <label for="agui-message-input" class="sr-only">Enter your research query</label>
        <textarea
          id="agui-message-input"
          bind:value={input}
          onkeydown={handleKeyDown}
          placeholder="Ask me anything... I'll research it for you with real-time streaming!"
          class="flex-1 resize-none border border-input bg-background rounded-md px-3 py-2 text-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 transition-all duration-200 hover:border-primary/30 min-h-[44px] disabled:cursor-not-allowed disabled:opacity-50"
          rows="2"
          disabled={$isLoading}
          aria-describedby="input-help"
          aria-label="Message input"
        ></textarea>
        <div id="input-help" class="sr-only">
          Press Enter to send message, Shift+Enter for new line
        </div>
        <button
          type="submit"
          disabled={!input.trim() || $isLoading}
          class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-3 flex-shrink-0 min-w-[44px]"
          aria-label="{$isLoading ? 'Processing request...' : 'Send message'}"
        >
          {#if $isLoading}
            <Loader2 class="w-4 h-4 animate-spin" />
          {:else}
            <Send class="w-4 h-4" />
          {/if}
        </button>
      </form>
    </footer>
  {/if}
</div>
