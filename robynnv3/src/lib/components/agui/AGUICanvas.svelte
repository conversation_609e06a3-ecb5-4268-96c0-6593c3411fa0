<script lang="ts">
  import { createEventDispatcher } from 'svelte'
  import { 
    Zap, 
    Search, 
    Building2, 
    TrendingUp,
    BarChart3,
    Lightbulb,
    Send,
    X,
    Plus
  } from 'lucide-svelte'
  import { conversationStore } from '$lib/agui/conversation-store'
  import { canvasStore, formatForPublishing, removeCanvasItem, clearCanvas } from '$lib/agui/canvas-store'
  import ResearchDisplay from '$lib/components/research/ResearchDisplay.svelte'
  import PublishingModal from '$lib/components/publishing/PublishingModal.svelte'

  const dispatch = createEventDispatcher()

  // Subscribe to conversation state
  let messages = $derived($conversationStore.messages)
  let isLoading = $derived($conversationStore.isLoading)
  let currentState = $derived($conversationStore.state)
  
  // Subscribe to canvas state with explicit reactivity
  let canvasState = $state({
    items: [],
    hasResearchData: false,
    isPublishable: false
  })
  
  let canvasItems = $derived(canvasState.items)
  let hasResearchData = $derived(canvasState.hasResearchData)
  let isPublishable = $derived(canvasState.isPublishable)
  
  // Subscribe to canvas store changes
  $effect(() => {
    const unsubscribe = canvasStore.subscribe(state => {
      console.log('Canvas store subscription triggered:', state)
      canvasState.items = state.items
      canvasState.hasResearchData = state.hasResearchData
      canvasState.isPublishable = state.isPublishable
    })
    return unsubscribe
  })
  
  // Debug canvas state changes
  $effect(() => {
    console.log('=== CANVAS STATE UPDATE ===')
    console.log('Canvas items count:', canvasItems.length)
    console.log('Has research data:', hasResearchData)
    console.log('Is publishable:', isPublishable)
    console.log('Canvas items:', canvasItems)
    console.log('============================')
  })
  
  // Get research data for publishing
  let researchItem = $derived(canvasItems.find(item => item.type === 'research'))
  let publishableData = $derived((() => {
    // If we have research data, use the standard formatting
    if (researchItem && researchItem.type === 'research') {
      return formatForPublishing(canvasItems)
    }
    
    // If we only have notes and tool results, create a basic publishable structure
    if (canvasItems.length > 0) {
      const notes = canvasItems.filter(item => item.type === 'note')
      const toolResults = canvasItems.filter(item => item.type === 'tool')
      
      return {
        targetCompany: {
          name: 'Product Marketing Research Canvas',
          description: 'Research compiled from canvas items'
        },
        metadata: {
          timestamp: new Date().toISOString(),
          source: 'canvas'
        },
        appendices: {
          notes: notes.map(note => ({
            title: note.type === 'note' ? note.title : undefined,
            content: note.type === 'note' ? note.markdown : '',
            timestamp: note.timestamp
          })),
          toolResults: toolResults.map(tool => ({
            name: tool.type === 'tool' ? tool.name : '',
            result: tool.type === 'tool' ? tool.result : {},
            timestamp: tool.timestamp
          }))
        }
      }
    }
    
    return null
  })())
  
  // Publishing modal state
  let showPublishingModal = $state(false)

  // Example prompts for different use cases
  const examplePrompts = [
    {
      category: "Company Research",
      icon: Building2,
      color: "bg-blue-500/10 hover:bg-blue-500/20 border-blue-500/20",
      iconColor: "text-blue-500",
      prompts: [
        "Research Stripe and analyze their business model",
        "Find information about OpenAI's latest products",
        "Compare Notion vs Monday.com features and pricing"
      ]
    },
    {
      category: "Market Analysis", 
      icon: TrendingUp,
      color: "bg-green-500/10 hover:bg-green-500/20 border-green-500/20",
      iconColor: "text-green-500",
      prompts: [
        "Analyze the AI automation market trends",
        "Research SaaS pricing strategies in 2024",
        "Find competitors in the productivity tools space"
      ]
    },
    {
      category: "Product Marketing",
      icon: Search,
      color: "bg-purple-500/10 hover:bg-purple-500/20 border-purple-500/20", 
      iconColor: "text-purple-500",
      prompts: [
        "Analyze competitive positioning for SaaS products",
        "Research customer personas for B2B platforms",
        "Find messaging strategies for enterprise software"
      ]
    },
    {
      category: "Industry Insights",
      icon: Lightbulb,
      color: "bg-orange-500/10 hover:bg-orange-500/20 border-orange-500/20",
      iconColor: "text-orange-500", 
      prompts: [
        "Research fintech innovation trends",
        "Analyze the future of remote work tools",
        "Find case studies of successful digital transformations"
      ]
    }
  ]

  function handlePromptClick(prompt: string) {
    dispatch('sendMessage', { message: prompt })
  }
  
  function openPublishingModal() {
    if (canvasItems.length > 0) {
      // Check if we have real research data or just placeholder fallback
      const hasRealResearchData = researchItem && researchItem.data && 
        researchItem.data.targetCompany &&
        researchItem.data.targetCompany.name !== 'Product Marketing Research Canvas'
      
      if (!hasRealResearchData && !researchItem) {
        // Show warning for canvas items without structured research data
        console.warn('Publishing canvas items without structured research data - will use fallback format')
      }
      
      showPublishingModal = true
    }
  }
  
  function handleClearCanvas() {
    clearCanvas()
  }

  // Get recent conversation insights
  let conversationInsights = $derived(messages.slice(-3).filter(m => m.role === 'assistant'))
  
  // Format markdown content for better display
  function formatMarkdownContent(markdown: string): string {
    let formatted = markdown
      // Convert headers
      .replace(/^### (.*$)/gm, '<h3 class="text-lg font-semibold mt-6 mb-3">$1</h3>')
      .replace(/^## (.*$)/gm, '<h2 class="text-xl font-semibold mt-6 mb-4">$1</h2>')
      .replace(/^# (.*$)/gm, '<h1 class="text-2xl font-bold mt-6 mb-4">$1</h1>')
      // Convert **bold** to <strong>
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      // Convert *italic* to <em>
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      
    // Handle lists properly by converting to HTML lists
    // First, collect all list items and wrap them in ul tags
    formatted = formatted.replace(/((?:^- .*$\n?)+)/gm, (match) => {
      const listItems = match.split('\n').filter(line => line.trim().startsWith('- '))
      const htmlItems = listItems.map(item => `<li>${item.substring(2)}</li>`).join('')
      return `<ul class="list-disc ml-6 mb-4">${htmlItems}</ul>`
    })
    
    // Handle numbered lists
    formatted = formatted.replace(/((?:^\d+\. .*$\n?)+)/gm, (match) => {
      const listItems = match.split('\n').filter(line => line.trim().match(/^\d+\. /))
      const htmlItems = listItems.map(item => `<li>${item.replace(/^\d+\. /, '')}</li>`).join('')
      return `<ol class="list-decimal ml-6 mb-4">${htmlItems}</ol>`
    })
    
    return formatted
      // Convert line breaks to <br> and handle paragraph breaks
      .replace(/\n\n/g, '</p><p>')
      .replace(/\n/g, '<br>')
      // Wrap in paragraph tags if not already wrapped
      .replace(/^(?!<[h123]|<[uo]l|<p>)(.*?)(?!<\/[h123]|<\/[uo]l>|<\/p>)$/gm, '<p>$1</p>')
      // Clean up empty paragraphs and fix double wrapping
      .replace(/<p><\/p>/g, '')
      .replace(/<p><p>/g, '<p>')
      .replace(/<\/p><\/p>/g, '</p>')
      // Clean up line breaks after headers and lists
      .replace(/(<\/h[123]>)<br>/g, '$1')
      .replace(/(<\/[uo]l>)<br>/g, '$1')
  }
</script>

<!-- Main Canvas Area -->
<div class="flex-1 bg-gradient-to-br from-background via-background to-muted/20 overflow-y-auto relative">
  <div class="max-w-6xl mx-auto p-6 space-y-8">
    
    <!-- Canvas Content (Dynamic based on canvas state) -->
    {#if hasResearchData && researchItem && researchItem.type === 'research'}
      <!-- Research Data Display -->
      <div class="space-y-6">
        <!-- Canvas Header with Actions -->
        <div class="bg-background border border-border rounded-xl p-4">
          <div class="flex items-center justify-between">
            <div>
              <h2 class="text-xl font-bold text-foreground">Product Marketing Research</h2>
              <p class="text-sm text-muted-foreground">Live research canvas • {canvasItems.length} items</p>
            </div>
            <div class="flex items-center gap-2">
              <!-- Debug info -->
              {#if canvasItems.length > 0}
                <span class="text-xs text-muted-foreground px-2 py-1 bg-muted rounded">
                  Items: {canvasItems.length} | Publishable: {isPublishable ? 'Yes' : 'No'} | Data: {publishableData ? 'Yes' : 'No'}
                </span>
              {/if}
              
              <button
                onclick={() => handleClearCanvas()}
                class="inline-flex items-center gap-2 px-3 py-2 text-sm text-muted-foreground hover:text-foreground border border-border rounded-md hover:bg-secondary transition-colors"
                title="Clear canvas"
              >
                <X class="w-4 h-4" />
                Clear
              </button>
            </div>
          </div>
        </div>
        
        <!-- Research Display Component -->
        <ResearchDisplay researchData={researchItem.data} />
        
        <!-- Additional Canvas Items -->
        {#each canvasItems.filter(item => item.type !== 'research') as item}
          <div class="bg-background border border-border rounded-xl p-6 relative group">
            <button
              onclick={() => removeCanvasItem(item.id)}
              class="absolute top-3 right-3 p-1 opacity-0 group-hover:opacity-100 transition-opacity text-muted-foreground hover:text-destructive"
              title="Remove item"
            >
              <X class="w-4 h-4" />
            </button>
            
            {#if item.type === 'note'}
              <div>
                {#if item.title}
                  <h3 class="text-lg font-semibold mb-4 text-foreground">{item.title}</h3>
                {/if}
                <div class="prose prose-base dark:prose-invert max-w-none prose-headings:text-foreground prose-strong:text-foreground prose-p:text-foreground prose-li:text-foreground">
                  {@html formatMarkdownContent(item.markdown)}
                </div>
                <div class="text-xs text-muted-foreground mt-4 pt-3 border-t border-border">
                  Added {item.timestamp.toLocaleString()}
                </div>
              </div>
            {:else if item.type === 'tool'}
              <div>
                <h3 class="font-semibold mb-3 flex items-center gap-2">
                  <BarChart3 class="w-4 h-4" />
                  Tool Result: {item.name}
                </h3>
                <div class="bg-muted/50 rounded-lg p-4 font-mono text-sm overflow-x-auto">
                  <pre>{JSON.stringify(item.result, null, 2)}</pre>
                </div>
                <div class="text-xs text-muted-foreground mt-3">
                  Executed {item.timestamp.toLocaleString()}
                </div>
              </div>
            {/if}
          </div>
        {/each}
      </div>
    {:else if (messages.length > 0 || isLoading) && !hasResearchData}
      <!-- Loading/Active Research State -->
      <div class="space-y-6">
        <!-- Canvas Header -->
        <div class="bg-background border border-border rounded-xl p-4">
          <div class="flex items-center justify-between">
            <div>
              <h2 class="text-xl font-bold text-foreground">Product Marketing Research</h2>
              <p class="text-sm text-muted-foreground">
                {isLoading ? 'Research in progress...' : `Active session • ${canvasItems.length} items`}
              </p>
            </div>
            <div class="flex items-center gap-2">
              <!-- Debug info -->
              {#if canvasItems.length > 0}
                <span class="text-xs text-muted-foreground px-2 py-1 bg-muted rounded">
                  Items: {canvasItems.length} | Publishable: {isPublishable ? 'Yes' : 'No'} | Data: {publishableData ? 'Yes' : 'No'}
                </span>
              {/if}
              
              <button
                onclick={() => handleClearCanvas()}
                class="inline-flex items-center gap-2 px-3 py-2 text-sm text-muted-foreground hover:text-foreground border border-border rounded-md hover:bg-secondary transition-colors"
                title="Clear canvas"
              >
                <X class="w-4 h-4" />
                Clear
              </button>
            </div>
          </div>
        </div>

        <!-- Canvas Items Display -->
        {#if canvasItems.length > 0}
          {#each canvasItems as item}
            <div class="bg-background border border-border rounded-xl p-6 relative group">
              <button
                onclick={() => removeCanvasItem(item.id)}
                class="absolute top-3 right-3 p-1 opacity-0 group-hover:opacity-100 transition-opacity text-muted-foreground hover:text-destructive"
                title="Remove item"
              >
                <X class="w-4 h-4" />
              </button>
              
              {#if item.type === 'note'}
                <div>
                  {#if item.title}
                    <h3 class="text-lg font-semibold mb-4 text-foreground">{item.title}</h3>
                  {/if}
                  <div class="prose prose-base dark:prose-invert max-w-none prose-headings:text-foreground prose-strong:text-foreground prose-p:text-foreground prose-li:text-foreground">
                    {@html formatMarkdownContent(item.markdown)}
                  </div>
                  <div class="text-xs text-muted-foreground mt-4 pt-3 border-t border-border">
                    Added {item.timestamp.toLocaleString()}
                  </div>
                </div>
              {:else if item.type === 'tool'}
                <div>
                  <h3 class="font-semibold mb-3 flex items-center gap-2">
                    <BarChart3 class="w-4 h-4" />
                    Tool Result: {item.name}
                  </h3>
                  <div class="bg-muted/50 rounded-lg p-4 font-mono text-sm overflow-x-auto">
                    <pre>{JSON.stringify(item.result, null, 2)}</pre>
                  </div>
                  <div class="text-xs text-muted-foreground mt-3">
                    Executed {item.timestamp.toLocaleString()}
                  </div>
                </div>
              {/if}
            </div>
          {/each}
        {:else if isLoading}
          <div class="bg-background border border-border rounded-xl p-6">
            <div class="flex items-center gap-3">
              <div class="w-6 h-6 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
              <span class="font-medium">Research in progress...</span>
            </div>
            <p class="text-sm text-muted-foreground mt-2">
              Your request is being processed with real-time streaming. Use "Add to Canvas" in the sidebar to build your report!
            </p>
          </div>
        {:else}
          <div class="bg-primary/5 border border-primary/20 rounded-xl p-6 text-center">
            <h3 class="font-semibold mb-2">Ready to Build Your Research Canvas</h3>
            <p class="text-sm text-muted-foreground">
              Use the "Add to Canvas" buttons in the sidebar to start building your product marketing research report.
            </p>
          </div>
        {/if}
      </div>
    {:else if messages.length === 0 && !isLoading}
      <!-- Hero Section -->
      <div class="text-center space-y-4 py-8">
        <div class="inline-flex items-center justify-center w-16 h-16 bg-primary/10 rounded-2xl mb-4">
          <Zap class="w-8 h-8 text-primary" />
        </div>
        <h1 class="text-4xl font-bold bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
          Product Marketing Research Agent
        </h1>
        <p class="text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed">
          Experience the power of real-time product marketing research with advanced streaming capabilities. 
          Get comprehensive competitive intelligence with live progress updates and tool execution visibility.
        </p>
      </div>


      <!-- Example Prompts -->
      <div class="space-y-6">
        <div class="text-center">
          <h2 class="text-2xl font-bold mb-2">Try These Research Examples</h2>
          <p class="text-muted-foreground">
            Click any prompt below to start a research session with real-time streaming
          </p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {#each examplePrompts as category}
            {@const IconComponent = category.icon}
            <div class="bg-background border border-border rounded-xl p-6 hover:shadow-lg transition-all duration-300">
              <div class="flex items-center gap-3 mb-4">
                <div class="w-10 h-10 {category.color} rounded-lg flex items-center justify-center border">
                  <IconComponent class="w-5 h-5 {category.iconColor}" />
                </div>
                <h3 class="font-semibold text-lg">{category.category}</h3>
              </div>
              
              <div class="space-y-2">
                {#each category.prompts as prompt}
                  <button
                    onclick={() => handlePromptClick(prompt)}
                    disabled={isLoading}
                    class="w-full text-left p-3 rounded-lg border border-border hover:bg-muted/50 hover:border-primary/30 transition-all duration-200 text-sm disabled:opacity-50 disabled:cursor-not-allowed group"
                  >
                    <div class="flex items-start gap-2">
                      <Search class="w-4 h-4 text-muted-foreground group-hover:text-primary transition-colors mt-0.5 flex-shrink-0" />
                      <span class="group-hover:text-foreground transition-colors">
                        {prompt}
                      </span>
                    </div>
                  </button>
                {/each}
              </div>
            </div>
          {/each}
        </div>
      </div>

      <!-- Loading State -->
      {#if isLoading}
        <div class="bg-background border border-border rounded-xl p-6">
          <div class="flex items-center gap-3">
            <div class="w-6 h-6 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
            <span class="font-medium">Research in progress...</span>
          </div>
          <p class="text-sm text-muted-foreground mt-2">
            Your request is being processed with real-time streaming. Check the sidebar for live updates!
          </p>
        </div>
      {/if}


    {/if}
  </div>
  

</div>

<!-- Publishing Modal -->
{#if canvasItems.length > 0}
  <PublishingModal
    bind:isOpen={showPublishingModal}
    researchData={publishableData || {
      targetCompany: { name: 'Canvas Content', description: 'Research from canvas items' },
      metadata: { timestamp: new Date().toISOString(), source: 'canvas' },
      appendices: { notes: [], toolResults: [] }
    }}
    title="Publish Product Marketing Research"
    on:close={() => showPublishingModal = false}
  />
{/if}
