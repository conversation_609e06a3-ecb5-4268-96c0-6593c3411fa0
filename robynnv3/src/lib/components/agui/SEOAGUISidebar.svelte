<script lang="ts">
  import { writable } from 'svelte/store'
  import { createEventDispatcher, tick } from 'svelte'
  import {
    Bot,
    User,
    Send,
    Loader2,
    X,
    Search,
    BarChart3,
    TrendingUp,
    Target,
    Zap,
    Link,
    RefreshCw,
    CheckCircle,
    Clock,
    Play,
    Pause
  } from 'lucide-svelte'
  import { fade, scale, fly, slide } from 'svelte/transition'
  import { quintOut, backOut } from 'svelte/easing'
  import { 
    seoConversationStore,
    seoMessages,
    seoIsLoading,
    seoProgressSteps,
    seoCurrentProgress,
    sendSEOMessage,
    clearSEOConversation,
    addSEOMessageToCanvas,
    type SEOConversationState
  } from '$lib/agui/seo-conversation-store'

  let { isOpen = $bindable(false) } = $props()
  
  const dispatch = createEventDispatcher()

  interface SEOMessage {
    id: string
    role: 'user' | 'assistant'
    content: string
    timestamp: Date
  }

  let input = $state('')
  let messagesContainer = $state<HTMLElement>()

  // SEO Strategist tools (matching the actual agent configuration)
  const seoTools = [
    { name: 'web-search', description: 'Web Search', icon: Search, category: 'research' },
    { name: 'get_keyword_volume', description: 'Keyword Volume', icon: BarChart3, category: 'seo' },
    { name: 'get_keyword_difficulty', description: 'Keyword Difficulty', icon: TrendingUp, category: 'seo' },
    { name: 'get_related_keywords', description: 'Related Keywords', icon: Link, category: 'seo' },
    { name: 'get_domain_intersection', description: 'Domain Intersection', icon: Zap, category: 'seo' },
    { name: 'get_keywords_for_site', description: 'Keywords for Site', icon: Target, category: 'seo' }
  ]

  // Subscribe to conversation state
  let messages = $derived($seoMessages)
  let isLoading = $derived($seoIsLoading)
  let progressSteps = $derived($seoProgressSteps)
  let currentProgress = $derived($seoCurrentProgress)
  
  // Progress calculations
  let progressPercentage = $derived(
    progressSteps.length > 0 
      ? Math.round((progressSteps.filter(s => s.status === 'completed').length / progressSteps.length) * 100)
      : 0
  )
  
  let activeStep = $derived(progressSteps.find(s => s.status === 'active'))
  let completedSteps = $derived(progressSteps.filter(s => s.status === 'completed').length)
  let totalSteps = $derived(progressSteps.length)

  // Scroll to bottom when new messages arrive
  $effect(() => {
    if (messages && messagesContainer) {
      tick().then(() => {
        if (messagesContainer) {
          messagesContainer.scrollTop = messagesContainer.scrollHeight
        }
      })
    }
  })

  async function handleSend() {
    if (!input.trim() || isLoading) return

    const message = input.trim()
    input = ''

    try {
      // Get the current URL to construct the endpoint
      const currentPath = window.location.pathname
      const endpoint = `${currentPath}?stream=true`
      
      console.log('=== SEO AG-UI SIDEBAR SEND MESSAGE ===')
      console.log('Message:', message)
      console.log('Endpoint:', endpoint)
      console.log('============================================')

      await sendSEOMessage(endpoint, message, {
        aguiMode: true,
        timestamp: new Date().toISOString()
      })
    } catch (error) {
      console.error('Error sending SEO message:', error)
      // Could add error toast here
    }
  }

  function handleKeydown(event: KeyboardEvent) {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault()
      handleSend()
    }
  }

  function handleAddToCanvas(content: string) {
    const result = addSEOMessageToCanvas(content, 'SEO Analysis')
    console.log('Added to canvas:', result)
  }

  function handleClearConversation() {
    if (confirm('Clear the entire conversation? This cannot be undone.')) {
      clearSEOConversation()
    }
  }
  
  // User control functions
  function handleCancelOperation() {
    seoConversationStore.update(state => {
      return {
        ...state,
        isLoading: false,
        progressSteps: state.progressSteps.map(step => ({
          ...step,
          status: step.status === 'active' ? 'pending' : step.status
        }))
      }
    })
  }
  
  function handlePauseOperation() {
    // For now, just stop loading state - actual pause implementation would need backend support
    seoConversationStore.update(state => ({
      ...state,
      isLoading: false
    }))
  }
  
  function handleResumeOperation() {
    // Resume would need to restart from last step
    console.log('Resume operation requested - implementation needed')
  }

  function formatMessageContent(content: string): string {
    if (!content) return ''
    
    // Handle specific JSON data formatting
    const trimmedContent = content.trim()
    if (trimmedContent.startsWith('{') && trimmedContent.endsWith('}')) {
      try {
        const jsonData = JSON.parse(trimmedContent)
        
        // If it's structured research data, format it nicely
        if (jsonData.targetCompany || jsonData.metadata || jsonData.companies || jsonData.recommendations) {
          let formatted = ''
          
          if (jsonData.targetCompany) {
            formatted += `<strong>Company:</strong> ${jsonData.targetCompany.name}<br>`
            if (jsonData.targetCompany.description) {
              formatted += `<em>${jsonData.targetCompany.description}</em><br><br>`
            }
          }
          
          if (jsonData.metadata) {
            formatted += `<strong>Analysis Confidence:</strong> ${Math.round((jsonData.metadata.confidence_score || 0) * 100)}%<br>`
            if (jsonData.metadata.processing_time) {
              formatted += `<strong>Processing Time:</strong> ${jsonData.metadata.processing_time}<br><br>`
            }
          }
          
          if (jsonData.companies && Array.isArray(jsonData.companies)) {
            formatted += `<strong>Companies Analyzed:</strong> ${jsonData.companies.length}<br>`
            jsonData.companies.slice(0, 3).forEach((company: any) => {
              if (company.name) {
                formatted += `• ${company.name}`
                if (company.confidence_score) {
                  formatted += ` (${Math.round(company.confidence_score * 100)}% confidence)`
                }
                formatted += '<br>'
              }
            })
          }
          
          if (jsonData.recommendations && Array.isArray(jsonData.recommendations)) {
            formatted += '<br><strong>Recommendations:</strong><br>'
            jsonData.recommendations.forEach((rec: any) => {
              if (rec.title) {
                formatted += `<strong>• ${rec.title}</strong><br>`
                if (rec.content_angle) {
                  formatted += `<em>Focus: ${rec.content_angle}</em><br>`
                }
                if (rec.target_keywords && Array.isArray(rec.target_keywords)) {
                  formatted += `Keywords: ${rec.target_keywords.join(', ')}<br>`
                }
                if (rec.priority_score) {
                  formatted += `Priority: ${rec.priority_score}/10<br>`
                }
                formatted += '<br>'
              }
            })
          }
          
          if (jsonData.market_analysis) {
            formatted += '<br><strong>Market Analysis:</strong><br>'
            if (jsonData.market_analysis.competitive_landscape) {
              const landscape = jsonData.market_analysis.competitive_landscape
              formatted += `• Competitors: ${landscape.total_competitors || 'N/A'}<br>`
              formatted += `• Market Dominance: ${Math.round((landscape.avg_dominance || 0) * 100)}%<br>`
            }
          }
          
          return formatted
        }
        
        // For other JSON, just prettify it
        return `<pre style="white-space: pre-wrap; font-family: monospace; font-size: 0.85em; background: rgba(0,0,0,0.05); padding: 8px; border-radius: 4px; overflow-x: auto;">${JSON.stringify(jsonData, null, 2)}</pre>`
      } catch (e) {
        // Not valid JSON, fall through to regular formatting
      }
    }
    
    // Basic formatting for SEO content
    return content
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/`(.*?)`/g, '<code>$1</code>')
      .replace(/\n/g, '<br>')
  }


</script>

<style>
  .sidebar-container {
    width: 0;
    min-width: 0;
    overflow: hidden;
  }
  
  .sidebar-open {
    width: 400px;
    min-width: 400px;
  }
  
  .sidebar-closed {
    width: 0;
    min-width: 0;
  }

  @media (max-width: 768px) {
    .sidebar-open {
      position: fixed;
      top: 0;
      right: 0;
      height: 100vh;
      z-index: 50;
      width: 100vw;
      min-width: 100vw;
    }
  }
</style>

<!-- Sidebar Container - Responsive positioning based on screen size -->
<div
  class="sidebar-container flex flex-col bg-card border-l border-border shadow-lg transition-all duration-400 ease-out"
  class:sidebar-open={isOpen}
  class:sidebar-closed={!isOpen}
  role="complementary"
  aria-label="SEO Agent Chat Sidebar"
  aria-live="polite"
>
  {#if isOpen}
    <!-- Header -->
    <div class="border-b border-border p-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-2">
          <Target class="h-5 w-5 text-primary" />
          <h2 class="text-lg font-semibold text-foreground">SEO Agent</h2>
        </div>
        <button
          onclick={() => isOpen = false}
          class="btn btn-sm btn-ghost hover:btn-error"
        >
          <X class="h-4 w-4" />
        </button>
      </div>
      
      <!-- SEO Tools -->
      <div class="mt-3">
        <div class="block text-sm font-medium text-foreground mb-2">Available SEO Tools</div>
        <div class="flex flex-wrap gap-1">
          {#each seoTools as tool, i}
            {@const IconComponent = tool.icon}
            <div
              in:scale={{ duration: 300, delay: 100 + (i * 50), easing: backOut }}
              class="transform hover:scale-105 transition-transform duration-200"
            >
              <div class="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium bg-muted rounded-md">
                <IconComponent class="w-3 h-3" />
                {tool.description}
              </div>
            </div>
          {/each}
        </div>
      </div>
    </div>



    <!-- Messages -->
    <div class="flex-1 overflow-y-auto p-4" bind:this={messagesContainer}>
      {#if messages.length === 0}
        <!-- Empty state - no placeholder content -->
        <div class="h-full"></div>
      {:else}
        <div class="space-y-4">
          {#each messages as message (message.id)}
            <!-- Only show messages with actual content or that are currently streaming -->
            {#if message.content && message.content.trim()}
              <div
                class="flex gap-3 {message.role === 'user' ? 'justify-end' : 'justify-start'}"
                transition:slide={{ duration: 200, easing: quintOut }}
              >
              {#if message.role === 'assistant'}
                <div class="w-8 h-8 rounded-full bg-primary flex items-center justify-center flex-shrink-0">
                  <Bot class="h-4 w-4 text-primary-content" />
                </div>
              {/if}
              
              <div class="flex-1 max-w-xs">
                <div
                  class="rounded-lg p-3 {message.role === 'user'
                    ? 'bg-primary text-primary-content ml-auto'
                    : 'bg-base-200 text-base-content'}"
                >
                  <div class="text-sm whitespace-pre-wrap">
                    {@html formatMessageContent(message.content)}
                  </div>
                  
                  {#if message.role === 'assistant'}
                    <div class="flex items-center gap-2 mt-2">
                      <button
                        onclick={() => handleAddToCanvas(message.content)}
                        class="btn btn-xs btn-ghost hover:btn-primary"
                        title="Add to canvas"
                      >
                        <Target class="h-3 w-3" />
                      </button>
                    </div>
                  {/if}
                </div>
                
                <div class="text-xs text-muted-foreground mt-1 {message.role === 'user' ? 'text-right' : ''}">
                  {message.timestamp.toLocaleTimeString()}
                </div>
              </div>
              
              {#if message.role === 'user'}
                <div class="w-8 h-8 rounded-full bg-base-300 flex items-center justify-center flex-shrink-0">
                  <User class="h-4 w-4 text-base-content" />
                </div>
              {/if}
              </div>
            {/if}
          {/each}
          
          {#if isLoading}
            <div class="flex gap-3 justify-start" transition:slide={{ duration: 200 }}>
              <div class="w-8 h-8 rounded-full bg-primary flex items-center justify-center flex-shrink-0">
                <Bot class="h-4 w-4 text-primary-content" />
              </div>
              <div class="bg-base-200 rounded-lg p-4 flex-1">
                <!-- Progress Header -->
                <div class="flex items-center justify-between mb-3">
                  <div class="flex items-center gap-2">
                    <Loader2 class="h-4 w-4 animate-spin text-primary" />
                    <span class="text-sm font-medium">
                      {activeStep ? activeStep.title : 'SEO Analysis'}
                    </span>
                  </div>
                  <div class="text-xs text-muted-foreground">
                    {progressPercentage}% complete
                  </div>
                </div>
                
                <!-- Progress Bar -->
                {#if totalSteps > 0}
                  <div class="w-full bg-base-300 rounded-full h-2 mb-3">
                    <div 
                      class="bg-primary h-2 rounded-full transition-all duration-500 ease-out"
                      style="width: {progressPercentage}%"
                    ></div>
                  </div>
                  
                  <!-- Step Counter -->
                  <div class="text-xs text-muted-foreground mb-3">
                    {completedSteps} of {totalSteps} tasks completed
                  </div>
                {/if}
                
                <!-- Current Task Description -->
                {#if activeStep?.description}
                  <div class="text-xs text-muted-foreground mb-3">
                    {activeStep.description}
                  </div>
                {/if}
                
                <!-- Progress Steps List -->
                {#if progressSteps.length > 0}
                  <div class="space-y-2">
                    {#each progressSteps as step (step.id)}
                      <div class="flex items-center gap-2 text-xs">
                        {#if step.status === 'completed'}
                          <CheckCircle class="h-3 w-3 text-success" />
                        {:else if step.status === 'active'}
                          <Loader2 class="h-3 w-3 animate-spin text-primary" />
                        {:else}
                          <Clock class="h-3 w-3 text-muted-foreground" />
                        {/if}
                        <span class="text-muted-foreground {step.status === 'active' ? 'font-medium text-foreground' : ''}">
                          {step.title}
                        </span>
                      </div>
                    {/each}
                  </div>
                {/if}
                
                <!-- User Controls -->
                <div class="flex gap-2 mt-4 pt-3 border-t border-border">
                  <button
                    onclick={handleCancelOperation}
                    class="btn btn-xs btn-error btn-outline"
                    title="Cancel analysis"
                  >
                    <X class="h-3 w-3" />
                    Cancel
                  </button>
                  <button
                    onclick={handlePauseOperation}
                    class="btn btn-xs btn-ghost"
                    title="Pause analysis"
                  >
                    <Pause class="h-3 w-3" />
                    Pause
                  </button>
                </div>
              </div>
            </div>
          {/if}
        </div>
      {/if}
    </div>



    <!-- Input -->
    <div class="border-t border-border p-4">
      <div class="flex gap-2">
        <div class="flex-1">
          <textarea
            bind:value={input}
            onkeydown={handleKeydown}
            class="textarea textarea-bordered w-full resize-none text-sm"
            rows="2"
            placeholder="Ask about SEO analysis, keywords, or content gaps..."
            disabled={isLoading}
          ></textarea>
        </div>
        <button
          onclick={handleSend}
          class="btn btn-primary"
          disabled={!input.trim() || isLoading}
        >
          {#if isLoading}
            <Loader2 class="h-4 w-4 animate-spin" />
          {:else}
            <Send class="h-4 w-4" />
          {/if}
        </button>
      </div>
      
      <!-- Controls -->
      {#if messages.length > 0}
        <div class="flex justify-end mt-2">
          <button
            onclick={handleClearConversation}
            class="btn btn-xs btn-ghost hover:btn-error"
          >
            <RefreshCw class="h-3 w-3" />
            Clear
          </button>
        </div>
      {/if}
    </div>
  {/if}
</div>
