<script lang="ts">
  import { createEventDispatcher } from 'svelte'
  import { 
    BarChart3,
    Download,
    Refresh<PERSON><PERSON>,
    Bo<PERSON>,
    User,
    Clock,
    CheckCircle,
    Circle
  } from 'lucide-svelte'
  import { 
    seoMessages,
    seoIsLoading,
    seoProgressSteps,
    seoCurrentProgress
  } from '$lib/agui/seo-conversation-store'
  import { 
    exportSEOCanvasData, 
    clearCanvas
  } from '$lib/agui/seo-canvas-store'
  import PublishingModal from '$lib/components/publishing/PublishingModal.svelte'

  const dispatch = createEventDispatcher()

  // Subscribe to conversation state
  let messages = $derived($seoMessages)
  let isLoading = $derived($seoIsLoading)
  
  // Debug logging for messages
  $effect(() => {
    console.log('=== SEO CANVAS MESSAGES DEBUG ===')
    console.log('Messages count:', messages?.length || 0)
    console.log('Messages details:', messages?.map(m => ({ 
      id: m.id, 
      role: m.role, 
      contentLength: m.content?.length || 0,
      contentPreview: m.content?.substring(0, 100) 
    })) || [])
    console.log('Is loading:', isLoading)
    console.log('=== END CANVAS MESSAGES DEBUG ===')
  })

  // Subscribe to progress state with safeguards
  let progressSteps = $derived($seoProgressSteps || [])
  let currentProgress = $derived($seoCurrentProgress || 0)

  // Calculate progress information with safeguards
  let progressPercentage = $derived(
    (progressSteps?.length > 0 && currentProgress >= 0) 
      ? Math.round((currentProgress / progressSteps.length) * 100) 
      : 0
  )
  let activeStep = $derived(
    progressSteps?.find(step => step.id === currentProgress) || null
  )
  let completedSteps = $derived(
    progressSteps?.filter(step => step.id < currentProgress) || []
  )
  let totalSteps = $derived(progressSteps?.length || 0)

  // Publishing modal state
  let showPublishingModal = $state(false)

  // Handle export as PDF
  async function handleExport() {
    try {
      // Get the actual content (not the flex container)
      let contentElement = document.querySelector('.seo-export-content .space-y-4') as HTMLElement
      if (!contentElement) {
        console.warn('Primary content element not found, trying alternative selectors')
        // Try alternative selectors
        contentElement = document.querySelector('.space-y-4') as HTMLElement
        if (!contentElement) {
          contentElement = document.querySelector('.seo-export-content') as HTMLElement
          if (!contentElement) {
            throw new Error('No exportable content found')
          }
        }
      }

      // Check if there's actual content to export
      if (!messages || messages.length === 0) {
        throw new Error('No content available to export')
      }

      // Debug the content element
      console.log('=== PDF EXPORT DEBUG ===')
      console.log('Content element:', contentElement)
      console.log('Element classes:', contentElement.className)
      console.log('Element dimensions:', {
        width: contentElement.offsetWidth,
        height: contentElement.offsetHeight,
        scrollWidth: contentElement.scrollWidth,
        scrollHeight: contentElement.scrollHeight
      })
      console.log('Element computed style background:', window.getComputedStyle(contentElement).backgroundColor)
      console.log('Content HTML preview:', contentElement.innerHTML.substring(0, 500))
      console.log('Messages count:', messages.length)
      console.log('=== END PDF DEBUG ===')

      // Dynamic import to avoid SSR issues
      const { exportToPDF } = await import('$lib/utils/pdf-generator')
      
      console.log('Starting PDF export...')
      
      // Export to PDF with custom options
      await exportToPDF(contentElement, {
        filename: `seo-analysis-${new Date().toISOString().slice(0, 10)}.pdf`,
        title: 'SEO Analysis Report',
        subtitle: `Generated on ${new Date().toLocaleDateString()}`,
        author: 'Robynn.ai',
        creator: 'Robynn.ai SEO Agent'
      })
      
      console.log('PDF export completed successfully')
    } catch (error) {
      console.error('PDF export failed:', error)
      
      // Show user-friendly error message
      alert('PDF export failed. Falling back to Markdown export.')
      
      // Fallback to markdown export
      handleMarkdownExport()
    }
  }

  // Keep markdown export as fallback
  function handleMarkdownExport() {
    const canvasData = exportSEOCanvasData()
    
    // Include the conversation messages from the store
    const exportData = {
      ...canvasData,
      messages: messages || [] // Pass the actual messages from the component state
    }
    
    const markdownContent = convertToMarkdown(exportData)
    const blob = new Blob([markdownContent], { type: 'text/markdown' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `seo-analysis-${new Date().toISOString().slice(0, 10)}.md`
    a.click()
    URL.revokeObjectURL(url)
  }

  // Convert data to Markdown format
  function convertToMarkdown(data: any): string {
    const timestamp = new Date().toLocaleDateString()
    let markdown = `# SEO Analysis Report\n\n*Generated on ${timestamp}*\n\n`
    
    // Include conversation messages if available
    if (data.messages && data.messages.length > 0) {
      markdown += `## Conversation History\n\n`
      
      data.messages.forEach((message: any, index: number) => {
        if (message.role === 'user') {
          markdown += `### Query ${index + 1}\n\n**${message.content}**\n\n`
        } else if (message.role === 'assistant') {
          // Convert HTML content back to clean text/markdown
          let cleanContent = message.content
            .replace(/<strong>(.*?)<\/strong>/g, '**$1**')
            .replace(/<em>(.*?)<\/em>/g, '*$1*')
            .replace(/<code[^>]*>(.*?)<\/code>/g, '`$1`')
            .replace(/<h[1-6][^>]*>(.*?)<\/h[1-6]>/g, '## $1')
            .replace(/<p[^>]*>(.*?)<\/p>/g, '$1\n\n')
            .replace(/<br\s*\/?>/g, '\n')
            .replace(/<[^>]*>/g, '') // Remove any remaining HTML tags
            .replace(/\n{3,}/g, '\n\n') // Clean up excessive newlines
          
          markdown += `${cleanContent}\n\n---\n\n`
        }
      })
    }
    
    // Include canvas data summary
    if (data.summary) {
      markdown += `## Analysis Summary\n\n`
      markdown += `- Total Items: ${data.summary.totalItems}\n`
      markdown += `- Has Research Data: ${data.summary.hasResearchData ? 'Yes' : 'No'}\n`
      markdown += `- Has SEO Analysis: ${data.summary.hasSEOAnalysis ? 'Yes' : 'No'}\n`
      markdown += `- Has Keyword Data: ${data.summary.hasKeywordData ? 'Yes' : 'No'}\n`
      markdown += `- Has Gap Analysis: ${data.summary.hasGapAnalysis ? 'Yes' : 'No'}\n`
      markdown += `- Has Content Strategy: ${data.summary.hasContentStrategy ? 'Yes' : 'No'}\n\n`
    }
    
    // Include keyword data if available
    if (data.data && data.data.keywords && data.data.keywords.length > 0) {
      markdown += `## Keyword Analysis\n\n`
      markdown += `| Keyword | Search Volume | Difficulty | Competition | CPC | Opportunity Score |\n`
      markdown += `|---------|---------------|------------|-------------|-----|-------------------|\n`
      
      data.data.keywords.forEach((keyword: any) => {
        markdown += `| ${keyword.keyword || 'N/A'} | ${keyword.search_volume || 0} | ${keyword.difficulty || 0} | ${keyword.competition || 'N/A'} | $${keyword.cpc || 0} | ${keyword.opportunity_score || 0} |\n`
      })
      markdown += `\n`
    }
    
    // Include gap analysis if available
    if (data.data && data.data.gaps && data.data.gaps.length > 0) {
      markdown += `## Gap Analysis\n\n`
      markdown += `| Keyword | Search Volume | Difficulty | Gap Type | Competitor Position | Opportunity Score |\n`
      markdown += `|---------|---------------|------------|----------|--------------------|-----------------|\n`
      
      data.data.gaps.forEach((gap: any) => {
        markdown += `| ${gap.keyword || 'N/A'} | ${gap.search_volume || 0} | ${gap.difficulty || 0} | ${gap.gap_type || 'N/A'} | ${gap.competitor_position || 'N/A'} | ${gap.opportunity_score || 0} |\n`
      })
      markdown += `\n`
    }
    
    // Fallback content if no data available
    if ((!data.messages || data.messages.length === 0) && (!data.data || Object.keys(data.data).length === 0)) {
      markdown += `## No Analysis Data Available\n\n`
      markdown += `This export contains no conversation history or analysis data. Please ensure you have completed an SEO analysis before exporting.\n\n`
    }
    
    markdown += `\n*Report generated by Robynn.ai SEO Agent*\n`
    return markdown
  }

  // Handle publishing
  function handlePublish() {
    showPublishingModal = true
  }

  // Handle clear
  function handleClearCanvas() {
    if (confirm('Are you sure you want to clear all messages?')) {
      clearCanvas()
    }
  }

  // Format message content with comprehensive formatting support
  function formatMessageContent(content: string): string {
    // Check if content contains JSON data and format it nicely
    if (content.trim().startsWith('{') && content.trim().endsWith('}')) {
      return formatJSONContent(content)
    }
    
    // Check if content contains table data (pipe-separated)
    // Look for lines with multiple pipes which indicate table structure
    const lines = content.split('\n')
    const hasTable = lines.some(line => {
      const pipeCount = (line.match(/\|/g) || []).length
      return pipeCount >= 3 && line.trim().length > 0 // At least 3 pipes indicates table structure
    })
    
    // Check if content contains JSON blocks within mixed content - enhanced detection
    const hasJSON = content.includes('{"') || content.includes('{\n') || 
                   content.includes('"recommendations":') || content.includes('"title":') ||
                   content.includes('"target_keywords":') || content.includes('"content_angle":')
    
    if (hasTable || hasJSON) {
      return formatMixedContent(content)
    }
    
    // Handle regular text content
    return formatTextParagraph(content)
  }

  // Format JSON content into readable sections
  function formatJSONContent(content: string): string {
    try {
      const data = JSON.parse(content)
      let formattedHTML = ''
      
      // Handle content strategy JSON structure
      if (data.recommendations && Array.isArray(data.recommendations)) {
        formattedHTML += `<div class="mb-6 p-4 bg-base-200 rounded-lg border border-border">
          <h3 class="text-xl font-bold text-primary mb-4">📋 Content Strategy Recommendations</h3>
          <div class="space-y-4">`
        
        data.recommendations.forEach((rec: any, index: number) => {
          formattedHTML += `<div class="bg-base-100 p-4 rounded-lg border border-border shadow-sm">
            <div class="flex items-start justify-between mb-2">
              <h4 class="text-lg font-semibold text-foreground">${index + 1}. ${rec.title || 'Content Recommendation'}</h4>
              ${rec.priority_score ? `<span class="bg-primary text-primary-content px-2 py-1 rounded text-sm font-medium">Priority: ${rec.priority_score}/10</span>` : ''}
            </div>
            
            ${rec.target_keywords && Array.isArray(rec.target_keywords) ? `
              <div class="mb-3">
                <p class="font-medium text-foreground mb-1">🎯 Target Keywords:</p>
                <div class="flex flex-wrap gap-1">
                  ${rec.target_keywords.map((keyword: string) => `<span class="bg-primary/10 text-primary px-2 py-1 rounded-full text-xs border border-primary/20">${keyword}</span>`).join('')}
                </div>
              </div>
            ` : ''}
            
            ${rec.content_angle ? `
              <div class="mb-3">
                <p class="font-medium text-foreground mb-1">💡 Content Angle:</p>
                <p class="text-muted-foreground text-sm leading-relaxed">${rec.content_angle}</p>
              </div>
            ` : ''}
            
            ${rec.expected_impact ? `
              <div class="mb-2">
                <p class="font-medium text-foreground mb-1">📈 Expected Impact:</p>
                <p class="text-muted-foreground text-sm">${rec.expected_impact}</p>
              </div>
            ` : ''}
          </div>`
        })
        
        formattedHTML += `</div>`
        
        if (data.overall_strategy) {
          formattedHTML += `<div class="mt-4 p-3 bg-primary/10 rounded-lg border border-primary/20">
            <p class="font-medium text-primary mb-1">🎯 Overall Strategy:</p>
            <p class="text-foreground text-sm leading-relaxed">${data.overall_strategy}</p>
          </div>`
        }
        
        formattedHTML += `</div>`
        return formattedHTML
      }
      
      // Handle company profile JSON structure
      if (data.name) {
        formattedHTML += `<div class="mb-6 p-4 bg-primary/10 rounded-lg border border-primary/20">
          <h3 class="text-xl font-bold text-primary mb-2">${data.name}</h3>
          ${data.domain ? `<p class="text-sm text-muted-foreground mb-2"><strong>Domain:</strong> ${data.domain}</p>` : ''}
        </div>`
      }
      
      if (data.overview) {
        formattedHTML += `<div class="mb-4">
          <h4 class="font-semibold mb-2">Business Overview</h4>
          <p class="text-foreground leading-relaxed">${data.overview}</p>
        </div>`
      }
      
      if (data.products_services && Array.isArray(data.products_services)) {
        formattedHTML += `<div class="mb-4">
          <h4 class="font-semibold mb-2">Products & Services</h4>
          <ul class="list-disc list-inside space-y-1">
            ${data.products_services.map((item: string) => `<li class="text-foreground">${item}</li>`).join('')}
          </ul>
        </div>`
      }
      
      if (data.competitors && Array.isArray(data.competitors)) {
        formattedHTML += `<div class="mb-4">
          <h4 class="font-semibold mb-2">Key Competitors</h4>
          <div class="flex flex-wrap gap-2">
            ${data.competitors.map((comp: string) => `<span class="px-2 py-1 bg-muted rounded text-sm">${comp}</span>`).join('')}
          </div>
        </div>`
      }
      
      if (data.target_audience) {
        formattedHTML += `<div class="mb-4">
          <h4 class="font-semibold mb-2">Target Audience</h4>
          <p class="text-foreground leading-relaxed">${data.target_audience}</p>
        </div>`
      }
      
      if (data.business_model) {
        formattedHTML += `<div class="mb-4">
          <h4 class="font-semibold mb-2">Business Model</h4>
          <p class="text-foreground leading-relaxed">${data.business_model}</p>
        </div>`
      }
      
      if (data.market_position) {
        formattedHTML += `<div class="mb-4">
          <h4 class="font-semibold mb-2">Market Position</h4>
          <p class="text-foreground leading-relaxed">${data.market_position}</p>
        </div>`
      }
      
      return formattedHTML
    } catch (e) {
      // If not valid JSON, treat as regular content
      return formatTextOrTableContent(content)
    }
  }

  // Format mixed content with JSON blocks, text, and tables
  function formatMixedContent(content: string): string {
    // Split content into sections and process each
    let formattedContent = ''
    let remainingContent = content
    
    // Look for various JSON patterns including nested ones and array structures
    // Handle both complete objects and fragments that might appear separately
    const jsonPatterns = [
      // Complete objects starting with { and ending with } - more aggressive matching
      /\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}/g,
      // Complete JSON objects with nested structures
      /\{[\s\S]*?\}/g,
      // JSON arrays
      /\[[\s\S]*?\]/g,
      // Specific content strategy patterns
      /"recommendations"\s*:\s*\[[\s\S]*?\]/g,
      // Property-like structures that might be JSON fragments
      /"[^"]+"\s*:\s*(\{[\s\S]*?\}|\[[\s\S]*?\]|"[^"]*"|\d+|true|false|null)/g
    ]
    
    let processedIndices = new Set()
    let segments = []
    
    // First pass: identify all JSON-like segments
    for (const pattern of jsonPatterns) {
      let match
      while ((match = pattern.exec(content)) !== null) {
        const start = match.index
        const end = match.index + match[0].length
        
        // Check if this segment overlaps with already processed ones
        let overlaps = false
        for (let i = start; i < end; i++) {
          if (processedIndices.has(i)) {
            overlaps = true
            break
          }
        }
        
        if (!overlaps) {
          try {
            JSON.parse(match[1] || match[0])
            segments.push({ start, end, content: match[1] || match[0], isJSON: true })
            for (let i = start; i < end; i++) {
              processedIndices.add(i)
            }
          } catch (e) {
            // Not valid JSON, ignore this match
          }
        }
      }
      pattern.lastIndex = 0 // Reset regex
    }
    
    // Sort segments by position
    segments.sort((a, b) => a.start - b.start)
    
    // Process content with identified segments
    let lastIndex = 0
    
    for (const segment of segments) {
      // Add text before this segment
      const textBefore = content.substring(lastIndex, segment.start).trim()
      if (textBefore) {
        formattedContent += formatTextOrTableContent(textBefore)
      }
      
      // Format the JSON segment
      if (segment.isJSON) {
        formattedContent += formatJSONContent(segment.content)
      } else {
        formattedContent += formatTextOrTableContent(segment.content)
      }
      
      lastIndex = segment.end
    }
    
    // Add any remaining text after the last segment
    const textAfter = content.substring(lastIndex).trim()
    if (textAfter) {
      formattedContent += formatTextOrTableContent(textAfter)
    }
    
    // If no segments were found, just format as text/table content
    if (segments.length === 0) {
      formattedContent = formatTextOrTableContent(content)
    }
    
    return formattedContent
  }
  
  // Helper function to format text and table content
  function formatTextOrTableContent(content: string): string {
    const lines = content.split('\n')
    let formattedContent = ''
    let currentParagraph = ''
    let inTable = false
    let tableRows: string[] = []
    
    for (const line of lines) {
      const pipeCount = (line.match(/\|/g) || []).length
      const isTableLine = pipeCount >= 3 && line.trim().length > 0
      
      if (isTableLine && !line.trim().startsWith('|--')) {
        // Start or continue table
        if (currentParagraph.trim()) {
          formattedContent += formatTextParagraph(currentParagraph)
          currentParagraph = ''
        }
        inTable = true
        tableRows.push(line)
      } else if (line.trim().startsWith('|--')) {
        // Skip separator lines
        continue
      } else {
        if (inTable && tableRows.length > 0) {
          // End of table, format it
          formattedContent += formatTable(tableRows)
          tableRows = []
          inTable = false
        }
        
        // Accumulate text lines for paragraph formatting
        if (line.trim() === '') {
          if (currentParagraph.trim()) {
            formattedContent += formatTextParagraph(currentParagraph)
            currentParagraph = ''
          }
        } else {
          currentParagraph += (currentParagraph ? '\n' : '') + line
        }
      }
    }
    
    // Handle remaining content
    if (inTable && tableRows.length > 0) {
      formattedContent += formatTable(tableRows)
    }
    if (currentParagraph.trim()) {
      formattedContent += formatTextParagraph(currentParagraph)
    }
    
    return formattedContent
  }
  
  // Format a text paragraph with professional styling
  function formatTextParagraph(text: string): string {
    return `<div class="mb-4 leading-relaxed text-foreground">${text
      .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold text-primary">$1</strong>')
      .replace(/\*(.*?)\*/g, '<em class="italic">$1</em>')
      .replace(/`(.*?)`/g, '<code class="bg-muted px-2 py-1 rounded text-sm font-mono">$1</code>')
      .replace(/#{3,}\s*(.*?)$/gm, '<h3 class="text-lg font-semibold mt-6 mb-3 text-primary">$1</h3>')
      .replace(/#{2}\s*(.*?)$/gm, '<h2 class="text-xl font-semibold mt-8 mb-4 text-primary">$1</h2>')
      .replace(/#{1}\s*(.*?)$/gm, '<h1 class="text-2xl font-bold mt-10 mb-5 text-primary">$1</h1>')
      .replace(/\n/g, '<br>')}</div>`
  }

  // Format table content specifically
  function formatTableContent(content: string): string {
    const lines = content.split('\n')
    let formattedContent = ''
    let inTable = false
    let tableRows: string[] = []
    
    for (const line of lines) {
      // Detect table lines - look for multiple pipes (at least 3)
      const pipeCount = (line.match(/\|/g) || []).length
      const isTableLine = pipeCount >= 3 && line.trim().length > 0
      
      if (isTableLine && !line.trim().startsWith('|--')) {
        inTable = true
        tableRows.push(line)
      } else if (line.trim().startsWith('|--')) {
        // Skip separator lines
        continue
      } else {
        if (inTable && tableRows.length > 0) {
          // End of table, format it
          formattedContent += formatTable(tableRows)
          tableRows = []
          inTable = false
        }
        // Add non-table content with professional formatting
        if (line.trim() !== '') {
          formattedContent += `<div class="mb-3 leading-relaxed">${line
            .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold">$1</strong>')
            .replace(/\*(.*?)\*/g, '<em class="italic">$1</em>')
            .replace(/`(.*?)`/g, '<code class="bg-muted px-2 py-1 rounded text-sm font-mono">$1</code>')
            .replace(/#{3,}\s*(.*?)$/g, '<h3 class="text-lg font-semibold mt-4 mb-2 text-primary">$1</h3>')
            .replace(/#{2}\s*(.*?)$/g, '<h2 class="text-xl font-semibold mt-6 mb-3 text-primary">$1</h2>')
            .replace(/#{1}\s*(.*?)$/g, '<h1 class="text-2xl font-bold mt-8 mb-4 text-primary">$1</h1>')}</div>`
        }
      }
    }
    
    // Handle remaining table if content ends with table
    if (inTable && tableRows.length > 0) {
      formattedContent += formatTable(tableRows)
    }
    
    return formattedContent
  }

  // Format table rows into HTML table
  function formatTable(rows: string[]): string {
    if (rows.length === 0) return ''
    
    let tableHTML = '<div class="overflow-x-auto w-full mb-6 shadow-sm border border-border rounded-lg"><table class="table table-zebra table-sm w-full">'
    
    // Header row
    if (rows[0]) {
      const headerCells = rows[0].split('|').map(cell => cell.trim()).filter(cell => cell !== '')
      tableHTML += '<thead class="bg-muted/50"><tr>'
      headerCells.forEach((cell, index) => {
        // Add specific styling for different column types
        let cellClass = 'font-semibold text-left py-3 px-4 text-foreground'
        if (index === 0) cellClass += ' w-16' // Rank column
        else if (cell.toLowerCase().includes('keyword')) cellClass += ' min-w-48' // Keyword column
        else if (cell.toLowerCase().includes('volume')) cellClass += ' w-24 text-right' // Volume column
        else if (cell.toLowerCase().includes('difficulty')) cellClass += ' w-24 text-right' // Difficulty column
        else if (cell.toLowerCase().includes('cpc')) cellClass += ' w-24 text-right' // CPC column
        
        tableHTML += `<th class="${cellClass}">${cell}</th>`
      })
      tableHTML += '</tr></thead>'
    }
    
    // Data rows
    tableHTML += '<tbody>'
    for (let i = 1; i < rows.length; i++) {
      const cells = rows[i].split('|').map(cell => cell.trim()).filter(cell => cell !== '')
      if (cells.length > 0) {
        tableHTML += '<tr class="hover:bg-muted/30 transition-colors">'
        cells.forEach((cell, index) => {
          let cellClass = 'py-2 px-4 text-sm'
          let cellContent = cell
          
          // Format specific column types
          if (index === 0) {
            // Rank column
            cellClass += ' font-medium text-center'
          } else if (index === 1) {
            // Keyword column - make it prominent
            cellClass += ' font-medium text-primary'
          } else if (cell.match(/^\d+$/)) {
            // Numeric columns - right align
            cellClass += ' text-right font-mono'
            // Add thousand separators for large numbers
            if (parseInt(cell) > 999) {
              cellContent = parseInt(cell).toLocaleString()
            }
          } else if (cell.match(/^\d+\.\d+$/)) {
            // Decimal numbers - right align
            cellClass += ' text-right font-mono'
          } else {
            // Text columns
            cellClass += ' text-muted-foreground'
          }
          
          tableHTML += `<td class="${cellClass}">${cellContent}</td>`
        })
        tableHTML += '</tr>'
      }
    }
    tableHTML += '</tbody></table></div>'
    
    return tableHTML
  }
</script>

<div class="flex-1 flex flex-col bg-background">
  <!-- Header -->
  <div class="border-b border-border p-4">
    <div class="flex items-center justify-between">
      <div class="flex items-center gap-2">
        <BarChart3 class="h-5 w-5 text-primary" />
        <h2 class="text-lg font-semibold text-foreground">SEO Analysis Canvas</h2>
        {#if messages.length > 0}
          <span class="text-sm text-muted-foreground">({messages.length} messages)</span>
        {/if}
      </div>
      
      <div class="flex items-center gap-2">
        <!-- Export button with dropdown -->
        {#if messages.length > 0}
          <div class="dropdown dropdown-end">
            <button
              tabindex="0"
              class="btn btn-sm btn-outline hover:btn-primary"
              title="Export options"
            >
              <Download class="h-4 w-4" />
            </button>
            <ul role="menu" class="dropdown-content menu bg-base-100 rounded-box z-[1] w-52 p-2 shadow border border-border">
              <li>
                <button type="button" onclick={handleExport} class="flex items-center gap-2">
                  <Download class="h-4 w-4" />
                  Export as PDF
                </button>
              </li>
              <li>
                <button type="button" onclick={handleMarkdownExport} class="flex items-center gap-2">
                  <Download class="h-4 w-4" />
                  Export as Markdown
                </button>
              </li>
            </ul>
          </div>
        {/if}
        
        <!-- Clear button -->
        {#if messages.length > 0}
          <button
            onclick={handleClearCanvas}
            class="btn btn-sm btn-ghost hover:btn-error"
            title="Clear canvas"
          >
            <RefreshCw class="h-4 w-4" />
          </button>
        {/if}
      </div>
    </div>
  </div>

  <!-- Content Area -->
  <div class="flex-1 overflow-y-auto p-4 seo-export-content">
    {#if messages.length === 0}
      <div class="h-full flex flex-col items-center justify-center text-center">
        <div class="w-16 h-16 rounded-full bg-base-200 flex items-center justify-center mx-auto mb-4">
          <BarChart3 class="h-8 w-8 text-muted-foreground" />
        </div>
        <h3 class="text-lg font-medium text-foreground mb-2">SEO Analysis Canvas</h3>
        <p class="text-muted-foreground text-sm max-w-xs mx-auto">
          Start a conversation with the SEO agent to see analysis results here.
        </p>
      </div>
    {:else}
      <div class="space-y-4">
        {#each messages as message (message.id)}
          {#if message.content && message.content.trim()}
            <div class="flex gap-3 {message.role === 'user' ? 'justify-end' : 'justify-start'}">
            {#if message.role === 'assistant'}
              <div class="w-8 h-8 rounded-full bg-primary flex items-center justify-center flex-shrink-0">
                <Bot class="h-4 w-4 text-primary-content" />
              </div>
            {/if}
            
            <div class="flex-1 {message.role === 'user' ? 'max-w-2xl' : 'w-full'}">
              <div
                class="rounded-lg p-4 {message.role === 'user'
                  ? 'bg-primary text-primary-content ml-auto'
                  : 'bg-base-200 text-base-content'}"
              >
                <div class="text-sm {message.role === 'user' ? 'whitespace-pre-wrap' : ''}">
                  {@html formatMessageContent(message.content)}
                </div>
              </div>
            </div>
            
            {#if message.role === 'user'}
              <div class="w-8 h-8 rounded-full bg-base-300 flex items-center justify-center flex-shrink-0">
                <User class="h-4 w-4 text-base-content" />
              </div>
            {/if}
            </div>
          {/if}
        {/each}
        
        <!-- Enhanced Loading indicator with progress -->
        {#if isLoading}
          <div class="flex gap-3 justify-start">
            <div class="w-8 h-8 rounded-full bg-primary flex items-center justify-center flex-shrink-0">
              <Bot class="h-4 w-4 text-primary-content animate-pulse" />
            </div>
            <div class="flex-1 w-full">
              <div class="bg-base-200 text-base-content rounded-lg p-4">
                {#if totalSteps > 0 && activeStep}
                  <!-- Progress header -->
                  <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center gap-2">
                      <Clock class="h-4 w-4 text-primary" />
                      <span class="text-sm font-medium">SEO Analysis in Progress</span>
                    </div>
                    <div class="flex items-center gap-1 text-xs text-muted-foreground">
                      <span>{Math.max(1, completedSteps?.length || 0)}</span>
                      <span>/</span>
                      <span>{totalSteps}</span>
                    </div>
                  </div>
                  
                  <!-- Progress bar -->
                  <div class="w-full bg-base-300 rounded-full h-2 mb-3">
                    <div 
                      class="bg-primary h-2 rounded-full transition-all duration-300 ease-out"
                      style="width: {Math.min(100, Math.max(0, progressPercentage))}%"
                    ></div>
                  </div>
                  
                  <!-- Current step -->
                  <div class="flex items-center gap-2 mb-3">
                    <div class="loading loading-spinner loading-xs text-primary"></div>
                    <span class="text-sm text-foreground">{activeStep?.title || 'Processing...'}</span>
                  </div>
                  
                  <!-- Step list -->
                  {#if progressSteps?.length > 0}
                    <div class="space-y-1">
                      {#each progressSteps as step}
                        <div class="flex items-center gap-2 text-xs">
                          {#if step.id < currentProgress}
                            <CheckCircle class="h-3 w-3 text-success" />
                            <span class="text-muted-foreground line-through">{step.title}</span>
                          {:else if step.id === currentProgress}
                            <div class="loading loading-spinner loading-xs text-primary"></div>
                            <span class="text-foreground font-medium">{step.title}</span>
                          {:else}
                            <Circle class="h-3 w-3 text-muted-foreground" />
                            <span class="text-muted-foreground">{step.title}</span>
                          {/if}
                        </div>
                      {/each}
                    </div>
                  {/if}
                {:else}
                  <!-- Fallback simple loading -->
                  <div class="flex items-center gap-2">
                    <div class="loading loading-dots loading-sm"></div>
                    <span class="text-sm text-muted-foreground">Analyzing...</span>
                  </div>
                {/if}
              </div>
            </div>
          </div>
        {/if}
      </div>
    {/if}
  </div>
</div>

<!-- Publishing Modal -->
{#if showPublishingModal}
  <PublishingModal
    bind:isOpen={showPublishingModal}
    researchData={{
      targetCompany: {
        name: "SEO Analysis",
        domain: "",
        industry: "",
        employees: "",
        revenue: "",
        description: "SEO Analysis Results",
        location: ""
      },
      metadata: {
        totalCompanies: 0,
        totalContacts: 0,
        processingTime: "0s",
        dataQuality: "High" as const,
        confidence_score: 0.9,
        data_freshness: "current",
        sources_used: ["SEO Agent"],
        api_calls_made: {},
        tool_status: {}
      },
      insights: messages,
      contacts: [],
      competitors: []
    }}
    title="SEO Analysis Results"
  />
{/if}
