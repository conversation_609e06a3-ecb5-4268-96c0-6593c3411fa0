<script lang="ts">
  import { onMount, createEventDispatcher } from "svelte"
  import {
    Bold,
    Italic,
    Underline,
    List,
    ListOrdered,
    Quote,
    Heading1,
    Heading2,
    Heading3,
    Undo,
    Redo,
    Save,
    FileText,
    Eye,
    Edit,
    Download,
    Clock,
  } from "lucide-svelte"
  import ConversationTimeline from "./ConversationTimeline.svelte"
  import { debounce } from "$lib/utils"
  import { markdownToHtml, countWords, estimateReadingTime } from "$lib/utils/markdown"

  export let content: string = ""
  export let title: string = "Untitled Document"
  export let isLoading: boolean = false
  export let autoSave: boolean = true
  export let placeholder: string = "Start writing your content..."
  export let markdownContent: string = ""
  export let contentMode: "single" | "accumulation" = "single"
  export let documentId: string | null = null
  export let envSlug: string = ""
  export let conversationSections: any[] = []

  const dispatch = createEventDispatcher()

  let editor: HTMLDivElement
  let titleInput: HTMLInputElement
  let wordCount = 0
  let characterCount = 0
  let lastSaved = new Date()
  let isSaving = false
  let hasUnsavedChanges = false
  let selectedText = ""
  let viewMode: "edit" | "preview" | "timeline" = "edit"
  let isMarkdownMode = false
  let timelineSectionsLoading = false

  // Debounced save function
  const debouncedSave = debounce(() => {
    if (autoSave && hasUnsavedChanges) {
      saveContent()
    }
  }, 2000)

  // Debounced content analysis
  const debouncedAnalyze = debounce(() => {
    analyzeContent()
  }, 500)

  // Reactive statements
  $: isMarkdownMode = contentMode === "accumulation"
  $: displayContent = isMarkdownMode && viewMode === "preview" 
    ? markdownToHtml(markdownContent) 
    : (isMarkdownMode ? markdownContent : content)
  
  // React to markdown content changes
  $: if (isMarkdownMode && markdownContent) {
    console.log('ContentCanvas - Markdown content updated:', {
      mode: contentMode,
      contentLength: markdownContent.length,
      viewMode
    })
    // Update the display when markdown content changes
    analyzeContent()
  }
  
  // React to mode changes
  $: if (contentMode) {
    console.log('ContentCanvas - Mode changed:', { 
      contentMode, 
      isMarkdownMode, 
      hasMarkdownContent: !!markdownContent,
      hasRegularContent: !!content
    })
  }
  
  // Force re-render when switching to markdown mode
  $: if (isMarkdownMode && editor) {
    // Clear the rich text editor when in markdown mode to prevent conflicts
    editor.innerHTML = ''
  }

  onMount(() => {
    if (!isMarkdownMode && editor) {
      editor.innerHTML = content
      analyzeContent()
    } else if (isMarkdownMode) {
      analyzeContent()
    }
  })

  // Reactive statement to update editor when content prop changes
  $: if (editor && !isMarkdownMode && content !== editor.innerHTML) {
    editor.innerHTML = content
    analyzeContent()
  }

  function formatText(command: string, value?: string) {
    document.execCommand(command, false, value)
    editor.focus()
    handleContentChange()
  }

  function handleContentChange() {
    if (editor) {
      content = editor.innerHTML
      hasUnsavedChanges = true
      debouncedAnalyze()
      debouncedSave()
      dispatch("contentChange", { content, title })
    }
  }

  function getSelectedText(): string {
    const selection = window.getSelection()
    if (selection && selection.rangeCount > 0) {
      return selection.toString().trim()
    }
    return ""
  }

  function handleSelectionChange() {
    const newSelectedText = getSelectedText()
    if (newSelectedText !== selectedText) {
      selectedText = newSelectedText
      dispatch("selectionChange", {
        selectedText,
        hasSelection: selectedText.length > 0,
        fullContent: editor?.innerText || ""
      })
    }
  }

  function replaceSelectedContent(newContent: string) {
    const selection = window.getSelection()
    if (selection && selection.rangeCount > 0 && selectedText) {
      const range = selection.getRangeAt(0)
      range.deleteContents()

      // Create a temporary div to parse HTML content
      const tempDiv = document.createElement('div')
      tempDiv.innerHTML = newContent

      // Insert the parsed content
      const fragment = document.createDocumentFragment()
      while (tempDiv.firstChild) {
        fragment.appendChild(tempDiv.firstChild)
      }
      range.insertNode(fragment)

      // Clear selection
      selection.removeAllRanges()
      selectedText = ""

      handleContentChange()
    } else {
      // If no selection, replace entire content
      if (editor) {
        editor.innerHTML = newContent
        handleContentChange()
      }
    }
  }

  function replaceEntireContent(newContent: string) {
    if (editor) {
      editor.innerHTML = newContent
      handleContentChange()
    }
  }

  // Export functions for parent component access
  export { getSelectedText, replaceSelectedContent, replaceEntireContent }

  function handleTitleChange() {
    hasUnsavedChanges = true
    debouncedSave()
    dispatch("titleChange", { title })
  }

  function analyzeContent() {
    if (isMarkdownMode && markdownContent) {
      wordCount = countWords(markdownContent)
      characterCount = markdownContent.length
    } else if (editor) {
      const text = editor.innerText || ""
      wordCount = text.trim() ? text.trim().split(/\s+/).length : 0
      characterCount = text.length
    }
  }

  async function saveContent() {
    if (isSaving) return

    isSaving = true
    try {
      // Include markdown content if in accumulation mode
      const saveData = isMarkdownMode 
        ? { content, title, markdownContent }
        : { content, title }
      await dispatch("save", saveData)
      hasUnsavedChanges = false
      lastSaved = new Date()
    } catch (error) {
      console.error("Save failed:", error)
    } finally {
      isSaving = false
    }
  }

  function insertHeading(level: number) {
    const selection = window.getSelection()
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0)
      const heading = document.createElement(`h${level}`)
      heading.textContent = selection.toString() || `Heading ${level}`

      range.deleteContents()
      range.insertNode(heading)

      // Move cursor after heading
      range.setStartAfter(heading)
      range.collapse(true)
      selection.removeAllRanges()
      selection.addRange(range)
    }
    handleContentChange()
  }

  function insertList(ordered: boolean = false) {
    const listType = ordered ? "insertOrderedList" : "insertUnorderedList"
    formatText(listType)
  }

  function insertBlockquote() {
    formatText("formatBlock", "blockquote")
  }

  function handleKeyDown(event: KeyboardEvent) {
    // Handle keyboard shortcuts
    if (event.ctrlKey || event.metaKey) {
      switch (event.key) {
        case "s":
          event.preventDefault()
          saveContent()
          break
        case "b":
          event.preventDefault()
          formatText("bold")
          break
        case "i":
          event.preventDefault()
          formatText("italic")
          break
        case "u":
          event.preventDefault()
          formatText("underline")
          break
        case "z":
          event.preventDefault()
          if (event.shiftKey) {
            formatText("redo")
          } else {
            formatText("undo")
          }
          break
      }
    }
  }

  function getReadingTime(): string {
    if (isMarkdownMode && markdownContent) {
      const minutes = estimateReadingTime(markdownContent)
      return `${minutes} min read`
    }
    const wordsPerMinute = 200
    const minutes = Math.ceil(wordCount / wordsPerMinute)
    return `${minutes} min read`
  }

  function toggleViewMode() {
    viewMode = viewMode === "edit" ? "preview" : "edit"
  }

  async function downloadMarkdown() {
    if (!documentId || !envSlug) return

    try {
      const response = await fetch(`/dashboard/${envSlug}/content-board?action=export&documentId=${documentId}&metadata=true`)
      if (!response.ok) throw new Error('Export failed')

      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.style.display = 'none'
      a.href = url
      a.download = response.headers.get('Content-Disposition')?.split('filename=')[1]?.replace(/"/g, '') || 'document.md'
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    } catch (error) {
      console.error('Download failed:', error)
    }
  }

  function formatLastSaved(): string {
    const now = new Date()
    const diff = now.getTime() - lastSaved.getTime()
    const minutes = Math.floor(diff / 60000)

    if (minutes < 1) return "Just now"
    if (minutes < 60) return `${minutes}m ago`

    const hours = Math.floor(minutes / 60)
    if (hours < 24) return `${hours}h ago`

    return lastSaved.toLocaleDateString()
  }

  function toggleTimelineView() {
    if (viewMode === "timeline") {
      viewMode = "edit"
    } else {
      viewMode = "timeline"
      loadTimelineSections()
    }
  }

  async function loadTimelineSections() {
    if (!documentId || !envSlug || timelineSectionsLoading) return

    timelineSectionsLoading = true
    try {
      const response = await fetch(`/dashboard/${envSlug}/content-board?action=sections&documentId=${documentId}`)
      if (response.ok) {
        const data = await response.json()
        conversationSections = data.sections || []
      }
    } catch (error) {
      console.error('Failed to load timeline sections:', error)
    } finally {
      timelineSectionsLoading = false
    }
  }

  function handleTimelineExport(event: CustomEvent) {
    const { sections, documentTitle } = event.detail
    
    // Create a comprehensive markdown export with timeline metadata
    let exportContent = `# ${documentTitle}\n\n`
    exportContent += `**Research Timeline Export**\n`
    exportContent += `Generated on: ${new Date().toLocaleString()}\n`
    exportContent += `Total Interactions: ${sections.length}\n\n`
    exportContent += `---\n\n`

    sections.forEach((section: any, index: number) => {
      exportContent += `## Section ${index + 1}: ${section.section_title}\n\n`
      if (section.original_prompt) {
        exportContent += `**Prompt:** ${section.original_prompt}\n\n`
      }
      exportContent += `**Source:** ${section.agent_source || 'Manual'}\n`
      exportContent += `**Created:** ${new Date(section.created_at).toLocaleString()}\n\n`
      exportContent += `${section.section_content}\n\n`
      exportContent += `---\n\n`
    })

    // Download the file
    const blob = new Blob([exportContent], { type: 'text/markdown' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${documentTitle.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_timeline.md`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  function handleSectionClick(event: CustomEvent) {
    const section = event.detail
    // Could implement section highlighting or jumping to section in editor
    console.log('Section clicked:', section)
    
    // Switch back to edit mode to show the content
    viewMode = "edit"
  }
</script>

<!-- Toolbar -->
<div class="border-b border-border bg-card p-4">
  <!-- Title Input -->
  <div class="mb-4">
    <input
      bind:this={titleInput}
      bind:value={title}
      on:input={handleTitleChange}
      class="w-full text-2xl font-bold bg-transparent border-none outline-none placeholder-muted-foreground"
      placeholder="Document title..."
      disabled={isLoading}
    />
  </div>

  <!-- Formatting Toolbar -->
  <div class="flex items-center gap-2 flex-wrap">
    <!-- Text Formatting -->
    <div class="flex items-center gap-1 border-r border-border pr-2">
      <button
        type="button"
        on:click={() => formatText("bold")}
        class="p-2 hover:bg-muted rounded transition-colors"
        title="Bold (Ctrl+B)"
        disabled={isLoading}
      >
        <Bold class="w-4 h-4" />
      </button>
      <button
        type="button"
        on:click={() => formatText("italic")}
        class="p-2 hover:bg-muted rounded transition-colors"
        title="Italic (Ctrl+I)"
        disabled={isLoading}
      >
        <Italic class="w-4 h-4" />
      </button>
      <button
        type="button"
        on:click={() => formatText("underline")}
        class="p-2 hover:bg-muted rounded transition-colors"
        title="Underline (Ctrl+U)"
        disabled={isLoading}
      >
        <Underline class="w-4 h-4" />
      </button>
    </div>

    <!-- Headings -->
    <div class="flex items-center gap-1 border-r border-border pr-2">
      <button
        type="button"
        on:click={() => insertHeading(1)}
        class="p-2 hover:bg-muted rounded transition-colors"
        title="Heading 1"
        disabled={isLoading}
      >
        <Heading1 class="w-4 h-4" />
      </button>
      <button
        type="button"
        on:click={() => insertHeading(2)}
        class="p-2 hover:bg-muted rounded transition-colors"
        title="Heading 2"
        disabled={isLoading}
      >
        <Heading2 class="w-4 h-4" />
      </button>
      <button
        type="button"
        on:click={() => insertHeading(3)}
        class="p-2 hover:bg-muted rounded transition-colors"
        title="Heading 3"
        disabled={isLoading}
      >
        <Heading3 class="w-4 h-4" />
      </button>
    </div>

    <!-- Lists and Quote -->
    <div class="flex items-center gap-1 border-r border-border pr-2">
      <button
        type="button"
        on:click={() => insertList(false)}
        class="p-2 hover:bg-muted rounded transition-colors"
        title="Bullet List"
        disabled={isLoading}
      >
        <List class="w-4 h-4" />
      </button>
      <button
        type="button"
        on:click={() => insertList(true)}
        class="p-2 hover:bg-muted rounded transition-colors"
        title="Numbered List"
        disabled={isLoading}
      >
        <ListOrdered class="w-4 h-4" />
      </button>
      <button
        type="button"
        on:click={insertBlockquote}
        class="p-2 hover:bg-muted rounded transition-colors"
        title="Quote"
        disabled={isLoading}
      >
        <Quote class="w-4 h-4" />
      </button>
    </div>

    <!-- Undo/Redo -->
    <div class="flex items-center gap-1 border-r border-border pr-2">
      <button
        type="button"
        on:click={() => formatText("undo")}
        class="p-2 hover:bg-muted rounded transition-colors"
        title="Undo (Ctrl+Z)"
        disabled={isLoading}
      >
        <Undo class="w-4 h-4" />
      </button>
      <button
        type="button"
        on:click={() => formatText("redo")}
        class="p-2 hover:bg-muted rounded transition-colors"
        title="Redo (Ctrl+Shift+Z)"
        disabled={isLoading}
      >
        <Redo class="w-4 h-4" />
      </button>
    </div>

    <!-- View Mode Toggle (only in markdown mode) -->
    {#if isMarkdownMode}
      <div class="flex items-center gap-1 border-r border-border pr-2">
        <button
          type="button"
          on:click={toggleViewMode}
          class="flex items-center gap-2 px-3 py-2 rounded transition-colors"
          class:bg-primary={viewMode === "preview"}
          class:text-primary-foreground={viewMode === "preview"}
          class:hover:bg-muted={viewMode === "edit"}
          title={viewMode === "edit" ? "Preview Markdown" : "Edit Markdown"}
          disabled={isLoading}
        >
          {#if viewMode === "edit"}
            <Eye class="w-4 h-4" />
            Preview
          {:else if viewMode === "preview"}
            <Edit class="w-4 h-4" />
            Edit
          {:else}
            <Edit class="w-4 h-4" />
            Edit
          {/if}
        </button>
        
        <button
          type="button"
          on:click={toggleTimelineView}
          class="flex items-center gap-2 px-3 py-2 rounded transition-colors"
          class:bg-primary={viewMode === "timeline"}
          class:text-primary-foreground={viewMode === "timeline"}
          class:hover:bg-muted={viewMode !== "timeline"}
          title="View Conversation Timeline"
          disabled={isLoading || timelineSectionsLoading}
        >
          <Clock class="w-4 h-4" />
          {viewMode === "timeline" ? "Timeline" : "Timeline"}
        </button>
      </div>

      <!-- Download Button -->
      <div class="flex items-center gap-1 border-r border-border pr-2">
        <button
          type="button"
          on:click={downloadMarkdown}
          class="flex items-center gap-2 px-3 py-2 hover:bg-muted rounded transition-colors"
          title="Download Markdown"
          disabled={isLoading}
        >
          <Download class="w-4 h-4" />
          Download
        </button>
      </div>
    {/if}

    <!-- Save Button -->
    <button
      type="button"
      on:click={saveContent}
      class="flex items-center gap-2 px-3 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90 transition-colors"
      disabled={isLoading || isSaving || !hasUnsavedChanges}
      title="Save (Ctrl+S)"
    >
      <Save class="w-4 h-4" />
      {#if isSaving}
        Saving...
      {:else if hasUnsavedChanges}
        Save
      {:else}
        Saved
      {/if}
    </button>
  </div>

  <!-- Status Bar -->
  <div
    class="flex items-center justify-between mt-3 text-sm text-muted-foreground"
  >
    <div class="flex items-center gap-4">
      <span>{wordCount} words</span>
      <span>{characterCount} characters</span>
      <span>{getReadingTime()}</span>
      {#if isMarkdownMode}
        <span class="text-primary">• Accumulation Mode</span>
      {/if}
    </div>
    <div class="flex items-center gap-2">
      {#if hasUnsavedChanges}
        <span class="text-orange-500">Unsaved changes</span>
      {:else}
        <span>Last saved: {formatLastSaved()}</span>
      {/if}
    </div>
  </div>
</div>

<!-- Editor -->
<div class="flex-1 overflow-hidden">
  {#if isMarkdownMode && viewMode === "timeline"}
    <!-- Conversation Timeline Mode -->
    <ConversationTimeline
      sections={conversationSections}
      documentTitle={title}
      isLoading={timelineSectionsLoading}
      on:export={handleTimelineExport}
      on:sectionClick={handleSectionClick}
    />
  {:else if isMarkdownMode && viewMode === "preview"}
    <!-- Markdown Preview Mode -->
    <div class="p-6 overflow-y-auto h-full">
      <div class="prose prose-slate max-w-none
                  prose-headings:text-foreground prose-p:text-foreground
                  prose-strong:text-foreground prose-em:text-foreground
                  prose-blockquote:text-muted-foreground prose-blockquote:border-border
                  prose-ul:text-foreground prose-ol:text-foreground prose-li:text-foreground">
        {@html displayContent}
      </div>
    </div>
  {:else if isMarkdownMode}
    <!-- Markdown Edit Mode -->
    <div class="p-6 h-full">
      <textarea
        bind:value={markdownContent}
        on:input={() => {
          hasUnsavedChanges = true
          debouncedAnalyze()
          debouncedSave()
        }}
        on:keydown={handleKeyDown}
        class="w-full h-full min-h-full resize-none border-none outline-none bg-transparent font-mono text-sm leading-relaxed"
        placeholder="Write your markdown content here..."
        disabled={isLoading}
      ></textarea>
    </div>
  {:else}
    <!-- Rich Text Editor Mode -->
    <div class="p-6 h-full overflow-y-auto">
      <div
        bind:this={editor}
        contenteditable="true"
        on:input={handleContentChange}
        on:keydown={handleKeyDown}
        on:mouseup={handleSelectionChange}
        on:keyup={handleSelectionChange}
        class="min-h-full outline-none prose prose-slate max-w-none
               prose-headings:text-foreground prose-p:text-foreground
               prose-strong:text-foreground prose-em:text-foreground
               prose-blockquote:text-muted-foreground prose-blockquote:border-border
               prose-ul:text-foreground prose-ol:text-foreground prose-li:text-foreground
               focus:outline-none"
        data-placeholder={placeholder}
        class:empty={!content}
        disabled={isLoading}
      ></div>
    </div>
  {/if}
</div>

<style>
  [contenteditable]:empty:before {
    content: attr(data-placeholder);
    color: hsl(var(--muted-foreground));
    pointer-events: none;
  }

  .prose blockquote {
    border-left: 4px solid hsl(var(--border));
    padding-left: 1rem;
    margin-left: 0;
    font-style: italic;
  }

  .prose h1,
  .prose h2,
  .prose h3 {
    margin-top: 2rem;
    margin-bottom: 1rem;
  }

  .prose p {
    margin-bottom: 1rem;
  }

  .prose ul,
  .prose ol {
    margin-bottom: 1rem;
  }
</style>
