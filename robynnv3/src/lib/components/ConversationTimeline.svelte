<script lang="ts">
  import { createEventDispatcher } from "svelte"
  import { 
    ChevronDown, 
    ChevronRight, 
    Clock, 
    Bot, 
    User, 
    Copy, 
    Download,
    Eye,
    EyeOff 
  } from "lucide-svelte"
  import type { ContentSection } from "$lib/supabase/supabase.types"

  export let sections: ContentSection[] = []
  export let documentTitle: string = ""
  export let isLoading: boolean = false

  const dispatch = createEventDispatcher()

  let expandedSections: Set<string> = new Set()
  let showDetails = true

  function toggleSection(sectionId: string) {
    if (expandedSections.has(sectionId)) {
      expandedSections.delete(sectionId)
    } else {
      expandedSections.add(sectionId)
    }
    expandedSections = new Set(expandedSections)
  }

  function toggleAllSections() {
    if (expandedSections.size === sections.length) {
      expandedSections.clear()
    } else {
      expandedSections = new Set(sections.map(s => s.id))
    }
    expandedSections = new Set(expandedSections)
  }

  function formatTimestamp(dateString: string) {
    return new Date(dateString).toLocaleString("en-US", {
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit"
    })
  }

  function getRelativeTime(dateString: string) {
    const now = new Date()
    const timestamp = new Date(dateString)
    const diffInMinutes = Math.floor((now.getTime() - timestamp.getTime()) / (1000 * 60))
    
    if (diffInMinutes < 1) return 'Just now'
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`
    
    const diffInHours = Math.floor(diffInMinutes / 60)
    if (diffInHours < 24) return `${diffInHours}h ago`
    
    const diffInDays = Math.floor(diffInHours / 24)
    return `${diffInDays}d ago`
  }

  function getAgentIcon(agentSource: string | null) {
    if (!agentSource || agentSource === 'manual') return User
    return Bot
  }

  function getAgentLabel(agentSource: string | null) {
    if (!agentSource || agentSource === 'manual') return 'Manual'
    return agentSource.split('-').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ')
  }

  async function copySection(section: ContentSection) {
    try {
      await navigator.clipboard.writeText(section.section_content)
      // Could add a toast notification here
    } catch (err) {
      console.error('Failed to copy section:', err)
    }
  }

  function exportTimeline() {
    dispatch('export', { sections, documentTitle })
  }

  function handleSectionClick(section: ContentSection) {
    dispatch('sectionClick', section)
  }

  // Sort sections by creation time and order index
  $: sortedSections = sections.sort((a, b) => {
    const orderDiff = a.order_index - b.order_index
    if (orderDiff !== 0) return orderDiff
    return new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
  })
</script>

<div class="flex flex-col h-full bg-background">
  <!-- Header -->
  <div class="flex items-center justify-between p-4 border-b border-border bg-card">
    <div>
      <h2 class="text-lg font-semibold text-foreground">Conversation Timeline</h2>
      <p class="text-sm text-muted-foreground">
        {sections.length} interaction{sections.length !== 1 ? 's' : ''} in "{documentTitle}"
      </p>
    </div>
    
    <div class="flex items-center gap-2">
      <button
        on:click={() => showDetails = !showDetails}
        class="flex items-center gap-1 px-2 py-1 text-xs border border-border bg-background hover:bg-accent transition-colors rounded"
        title={showDetails ? 'Hide details' : 'Show details'}
      >
        <svelte:component this={showDetails ? EyeOff : Eye} class="w-3 h-3" />
        {showDetails ? 'Hide' : 'Show'} Details
      </button>
      
      <button
        on:click={toggleAllSections}
        class="flex items-center gap-1 px-2 py-1 text-xs border border-border bg-background hover:bg-accent transition-colors rounded"
      >
        {expandedSections.size === sections.length ? 'Collapse All' : 'Expand All'}
      </button>
      
      <button
        on:click={exportTimeline}
        class="flex items-center gap-1 px-2 py-1 text-xs bg-primary text-primary-foreground hover:bg-primary/90 transition-colors rounded"
      >
        <Download class="w-3 h-3" />
        Export
      </button>
    </div>
  </div>

  <!-- Timeline Content -->
  <div class="flex-1 overflow-y-auto p-4">
    {#if isLoading}
      <!-- Loading State -->
      <div class="space-y-4">
        {#each Array(3) as _}
          <div class="flex gap-3">
            <div class="w-8 h-8 bg-muted animate-pulse rounded-full flex-shrink-0"></div>
            <div class="flex-1 space-y-2">
              <div class="h-4 bg-muted animate-pulse rounded w-3/4"></div>
              <div class="h-20 bg-muted animate-pulse rounded"></div>
            </div>
          </div>
        {/each}
      </div>
    {:else if sortedSections.length === 0}
      <!-- Empty State -->
      <div class="flex flex-col items-center justify-center h-32 text-center">
        <Clock class="w-12 h-12 text-muted-foreground mb-3" />
        <h3 class="text-sm font-medium text-foreground mb-1">No interactions yet</h3>
        <p class="text-xs text-muted-foreground">
          Start a conversation to see the timeline
        </p>
      </div>
    {:else}
      <!-- Timeline Items -->
      <div class="relative">
        <!-- Timeline Line -->
        <div class="absolute left-4 top-8 bottom-0 w-px bg-border"></div>
        
        <div class="space-y-6">
          {#each sortedSections as section, index}
            <div class="relative flex gap-4">
              <!-- Timeline Node -->
              <div class="relative z-10 flex-shrink-0">
                <div class="w-8 h-8 rounded-full border-2 border-border bg-card flex items-center justify-center">
                  <svelte:component 
                    this={getAgentIcon(section.agent_source)} 
                    class="w-4 h-4 text-muted-foreground" 
                  />
                </div>
              </div>

              <!-- Content Card -->
              <div class="flex-1 min-w-0">
                <div class="bg-card border border-border rounded-lg overflow-hidden">
                  <!-- Section Header -->
                  <div 
                    class="p-3 cursor-pointer hover:bg-muted/50 transition-colors"
                    on:click={() => toggleSection(section.id)}
                  >
                    <div class="flex items-center justify-between">
                      <div class="flex items-center gap-2 min-w-0">
                        <button class="flex-shrink-0">
                          <svelte:component 
                            this={expandedSections.has(section.id) ? ChevronDown : ChevronRight} 
                            class="w-4 h-4 text-muted-foreground" 
                          />
                        </button>
                        <h3 class="font-medium text-foreground truncate">
                          {section.section_title}
                        </h3>
                        
                        {#if showDetails}
                          <span class="px-2 py-0.5 text-xs bg-muted text-muted-foreground rounded">
                            {getAgentLabel(section.agent_source)}
                          </span>
                        {/if}
                      </div>
                      
                      <div class="flex items-center gap-2 text-xs text-muted-foreground">
                        <span>{getRelativeTime(section.created_at)}</span>
                        {#if showDetails}
                          <span>•</span>
                          <span>{formatTimestamp(section.created_at)}</span>
                        {/if}
                      </div>
                    </div>
                    
                    {#if showDetails && section.original_prompt}
                      <div class="mt-2 text-xs text-muted-foreground italic">
                        <span class="font-medium">Prompt:</span> "{section.original_prompt}"
                      </div>
                    {/if}
                  </div>

                  <!-- Expanded Content -->
                  {#if expandedSections.has(section.id)}
                    <div class="border-t border-border bg-muted/20">
                      <div class="p-4">
                        <div class="prose prose-sm max-w-none text-foreground">
                          {section.section_content}
                        </div>
                        
                        <!-- Section Actions -->
                        <div class="flex items-center justify-between mt-4 pt-3 border-t border-border">
                          <div class="text-xs text-muted-foreground">
                            Order: #{section.order_index + 1}
                            {#if section.metadata && Object.keys(section.metadata).length > 0}
                              • {Object.keys(section.metadata).length} metadata field{Object.keys(section.metadata).length !== 1 ? 's' : ''}
                            {/if}
                          </div>
                          
                          <div class="flex items-center gap-2">
                            <button
                              on:click|stopPropagation={() => copySection(section)}
                              class="flex items-center gap-1 px-2 py-1 text-xs border border-border bg-background hover:bg-accent transition-colors rounded"
                              title="Copy section content"
                            >
                              <Copy class="w-3 h-3" />
                              Copy
                            </button>
                            
                            <button
                              on:click|stopPropagation={() => handleSectionClick(section)}
                              class="flex items-center gap-1 px-2 py-1 text-xs bg-primary text-primary-foreground hover:bg-primary/90 transition-colors rounded"
                              title="View in editor"
                            >
                              View
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  {/if}
                </div>
              </div>
            </div>
          {/each}
        </div>
      </div>
    {/if}
  </div>
</div>

<style>
  .prose {
    color: inherit;
  }
  .prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
    color: inherit;
    margin-top: 1em;
    margin-bottom: 0.5em;
  }
  .prose p {
    margin-bottom: 1em;
  }
  .prose ul, .prose ol {
    padding-left: 1.5em;
    margin-bottom: 1em;
  }
  .prose li {
    margin-bottom: 0.25em;
  }
</style>