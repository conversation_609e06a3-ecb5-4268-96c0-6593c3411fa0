<script lang="ts">
  import { createEventDispatcher } from "svelte"
  import { page } from "$app/stores"
  import { ChevronDown, ChevronRight, Brain, Users, Wrench, FileText, Search, BarChart3, Settings, LogOut } from "lucide-svelte"

  export let session: any
  export let environment: any
  export let allTools: any[] = []
  export let categoryStats: any = {}
  export let selectedSection: string | null = null
  export let selectedAgent: string | null = null
  export let selectedTool: any = null

  const dispatch = createEventDispatcher()

  // Panel sections state
  let agentsExpanded = true
  let toolsExpanded = false

  // Agent definitions
  const agents = [
    {
      id: 'pmm-research-agent',
      name: 'Product Marketing Manager',
      description: 'Elite market intelligence & AG-UI enabled',
      icon: BarChart3,
      color: 'text-blue-500',
      route: `/dashboard/${$page.params.envSlug}/pmm-agent`
    },
    {
      id: 'agent-seo',
      name: 'SEO Agent',
      description: 'Keyword research & strategy',
      icon: Search,
      color: 'text-green-500',
      route: `/dashboard/${$page.params.envSlug}/agent-seo`
    },
    {
      id: 'content-agent',
      name: 'Content Agent',
      description: 'Content creation & editing',
      icon: FileText,
      color: 'text-purple-500',
      route: `/dashboard/${$page.params.envSlug}/content-agent`
    }
  ]

  function toggleAgentsSection() {
    agentsExpanded = !agentsExpanded
  }

  function toggleToolsSection() {
    toolsExpanded = !toolsExpanded
  }

  function handleContentBoardClick() {
    dispatch('contentBoardSelect')
    dispatch('sectionSelect', { section: 'content-board' })
  }

  function handleAgentsHeaderClick() {
    dispatch('sectionSelect', { section: 'agents' })
  }

  function handleToolsHeaderClick() {
    dispatch('sectionSelect', { section: 'tools' })
  }

  function handleAgentClick(agent: any) {
    // Navigate directly to agent page without updating dashboard state
    window.location.href = agent.route
  }

  function handleToolClick(tool: any) {
    dispatch('toolSelect', { tool })
    dispatch('sectionSelect', { section: 'tools', tool })
    dispatch('toolClick', tool)
  }

  function handleToolCategoryClick(category: string) {
    dispatch('sectionSelect', { section: 'tools', category })
  }

  // Get tool categories for display
  $: toolCategories = Object.keys(categoryStats || {})
</script>

<div class="flex flex-col h-full bg-card">
  <!-- Header -->
  <div class="p-4 border-b border-border">
    <p class="text-xs text-muted-foreground">Your augmented marketing team</p>
  </div>

  <!-- Navigation Content -->
  <div class="flex-1 overflow-y-auto p-4 space-y-6">
    
    <!-- Content Board - Primary Tier -->
    <div class="space-y-2">
      <button
        on:click={handleContentBoardClick}
        class="w-full p-4 bg-card border border-border hover:border-primary/50 rounded-lg transition-all duration-200 hover:shadow-md group {selectedSection === 'content-board' ? 'border-primary shadow-md' : ''}"
      >
        <div class="flex items-center gap-3">
          <div class="w-10 h-10 bg-muted rounded-lg flex items-center justify-center">
          <Brain class="w-5 h-5 text-blue-500" />
          </div>
          <div class="flex-1 text-left">
            <div class="font-semibold text-foreground group-hover:text-primary transition-colors">
              Content Board
            </div>
            <div class="text-xs text-muted-foreground">
              Your Knowledge Foundation
            </div>
          </div>
        </div>
        <div class="mt-2 flex items-center justify-between">
          <span class="inline-flex items-center px-2 py-1 text-xs font-bold bg-primary/20 text-primary rounded-full border border-primary/30">
            Foundation
          </span>
          <ChevronRight class="w-4 h-4 text-muted-foreground group-hover:text-primary transition-colors" />
        </div>
      </button>
    </div>

    <!-- Agents - Secondary Tier -->
    <div class="space-y-2">
      <div class="flex items-center gap-2">
        <button
          on:click={toggleAgentsSection}
          class="flex items-center gap-2 text-sm font-semibold text-foreground hover:text-primary transition-colors"
        >
          {#if agentsExpanded}
            <ChevronDown class="w-4 h-4" />
          {:else}
            <ChevronRight class="w-4 h-4" />
          {/if}
        </button>
        <button
          on:click={handleAgentsHeaderClick}
          class="flex items-center gap-2 text-sm font-semibold text-foreground hover:text-primary transition-colors flex-1"
        >
          <Users class="w-4 h-4" />
          <span>Marketing Agents</span>
        </button>
      </div>

      {#if agentsExpanded}
        <div class="ml-6 space-y-2">
          {#each agents as agent}
            <button
              on:click={() => handleAgentClick(agent)}
              class="w-full p-3 bg-card border border-border hover:border-accent hover:bg-accent/50 rounded-lg transition-all duration-200 text-left group {selectedAgent === agent.id ? 'border-accent bg-accent/50' : ''}"
            >
              <div class="flex items-center gap-3">
                <div class="w-8 h-8 bg-secondary text-secondary-foreground rounded-lg flex items-center justify-center">
                  <svelte:component this={agent.icon} class="w-4 h-4 {agent.color}" />
                </div>
                <div class="flex-1">
                  <div class="font-medium text-foreground text-sm group-hover:text-accent-foreground transition-colors">
                    {agent.name}
                  </div>
                  <div class="text-xs text-muted-foreground">
                    {agent.description}
                  </div>
                </div>
              </div>
              <div class="mt-2">
                <span class="inline-flex items-center px-2 py-0.5 text-xs font-medium bg-secondary/50 text-secondary-foreground rounded-full border border-secondary">
                  Intelligence
                </span>
              </div>
            </button>
          {/each}
        </div>
      {/if}
    </div>

    <!-- Tools - Tertiary Tier -->
    <div class="space-y-2">
      <div class="flex items-center gap-2">
        <button
          on:click={toggleToolsSection}
          class="flex items-center gap-2 text-sm font-semibold text-foreground hover:text-primary transition-colors"
        >
          {#if toolsExpanded}
            <ChevronDown class="w-4 h-4" />
          {:else}
            <ChevronRight class="w-4 h-4" />
          {/if}
        </button>
        <button
          on:click={handleToolsHeaderClick}
          class="flex items-center gap-2 text-sm font-semibold text-foreground hover:text-primary transition-colors flex-1"
        >
          <Wrench class="w-4 h-4" />
          <span>Tools</span>
          <span class="text-xs text-muted-foreground">({allTools.length})</span>
        </button>
      </div>

      {#if toolsExpanded}
        <div class="ml-6 space-y-1">
          {#each toolCategories.slice(0, 5) as category}
            <button
              on:click={() => handleToolCategoryClick(category)}
              class="w-full p-2 bg-muted/50 hover:bg-muted rounded-lg transition-colors text-left group"
            >
              <div class="flex items-center justify-between">
                <span class="text-xs font-medium text-foreground capitalize">
                  {category}
                </span>
                <span class="text-xs text-muted-foreground bg-muted px-1.5 py-0.5 rounded-full">
                  {categoryStats[category] || 0}
                </span>
              </div>
            </button>
          {/each}
          
          {#if toolCategories.length > 5}
            <button
              on:click={() => dispatch('sectionSelect', { section: 'tools' })}
              class="w-full p-2 text-xs text-muted-foreground hover:text-foreground transition-colors text-center"
            >
              View all tools...
            </button>
          {/if}
        </div>
      {/if}
    </div>
  </div>

  <!-- Footer -->
  <div class="p-4 border-t border-border space-y-3">
    <!-- Settings and Sign Out -->
    <div class="flex items-center gap-2">
      <a
        href="/dashboard/{$page.params.envSlug}/settings"
        class="flex items-center gap-2 px-3 py-2 text-sm font-medium text-muted-foreground hover:text-foreground hover:bg-muted/50 rounded-lg transition-colors flex-1"
      >
        <Settings class="w-4 h-4" />
        <span>Settings</span>
      </a>
      <a
        href="/dashboard/{environment?.slug}/../../sign_out"
        class="flex items-center gap-2 px-3 py-2 text-sm font-medium text-muted-foreground hover:text-foreground hover:bg-muted/50 rounded-lg transition-colors flex-1"
      >
        <LogOut class="w-4 h-4" />
        <span>Sign Out</span>
      </a>
    </div>

    <!-- Environment Info -->
    <div class="text-xs text-muted-foreground">
      Environment: <span class="font-medium">{environment?.name || 'Default'}</span>
    </div>
  </div>
</div>
