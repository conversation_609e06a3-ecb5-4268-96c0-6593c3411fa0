<script lang="ts">
  import { createEventDispatcher } from "svelte"
  import { 
    Plus, 
    Search, 
    Grid3X3, 
    List, 
    MoreVertical, 
    Trash2, 
    FileText,
    Menu,
    MessageSquare,
    Play,
    Pause,
    CheckCircle,
    Clock,
    Hash,
    RefreshCw
  } from "lucide-svelte"

  export let documents: any[] = []
  export let contentTypes: any[] = []
  export let selectedContentType: string = "all"
  export let searchTerm: string = ""
  export let isLoading: boolean = false
  export let leftPanelOpen: boolean = true
  export let rightPanelOpen: boolean = false
  export let selectedExternalSource: string | null = null
  export let externalSourceData: any = {}

  const dispatch = createEventDispatcher()

  let viewMode: 'grid' | 'list' = 'grid'
  let showDropdown: string | null = null

  function handleCreateDocument() {
    dispatch('createDocument')
  }

  function handleOpenDocument(doc: any) {
    dispatch('openDocument', doc)
  }

  function handleDeleteDocument(docId: string) {
    dispatch('deleteDocument', docId)
  }

  function handleSearch() {
    dispatch('searchDocuments')
  }

  function handleContentTypeChange() {
    dispatch('loadDocuments')
  }

  function toggleLeftPanel() {
    dispatch('toggleLeftPanel')
  }

  function toggleRightPanel() {
    dispatch('toggleRightPanel')
  }

  function formatDate(dateString: string) {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  function formatReadingTime(minutes: number) {
    if (minutes < 1) return "< 1 min read"
    return `${minutes} min read`
  }

  function getContentTypeIcon(type: string) {
    switch (type) {
      case 'article': return '📄'
      case 'blog-post': return '📝'
      case 'whitepaper': return '📋'
      case 'social-media': return '📱'
      case 'email': return '✉️'
      case 'documentation': return '📚'
      case 'press-release': return '📢'
      case 'case-study': return '🔍'
      case 'newsletter': return '📰'
      case 'landing-page': return '🎯'
      default: return '📄'
    }
  }

  function getContentTypeColor(type: string) {
    switch (type) {
      case 'article': return 'bg-blue-50 text-blue-700 border-blue-200'
      case 'blog-post': return 'bg-green-50 text-green-700 border-green-200'
      case 'whitepaper': return 'bg-purple-50 text-purple-700 border-purple-200'
      case 'social-media': return 'bg-pink-50 text-pink-700 border-pink-200'
      case 'email': return 'bg-orange-50 text-orange-700 border-orange-200'
      case 'documentation': return 'bg-indigo-50 text-indigo-700 border-indigo-200'
      case 'press-release': return 'bg-red-50 text-red-700 border-red-200'
      case 'case-study': return 'bg-yellow-50 text-yellow-700 border-yellow-200'
      case 'newsletter': return 'bg-teal-50 text-teal-700 border-teal-200'
      case 'landing-page': return 'bg-cyan-50 text-cyan-700 border-cyan-200'
      default: return 'bg-gray-50 text-gray-700 border-gray-200'
    }
  }

  function getExternalSourceTitle(sourceId: string) {
    switch (sourceId) {
      case 'websites': return 'Connected Websites'
      case 'slack': return 'Connected Slack Channels'
      case 'gdrive': return 'Connected Google Drive Files'
      default: return 'External Source'
    }
  }

  function formatLastModified(dateString: string) {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
    })
  }

  function toggleDropdown(docId: string) {
    showDropdown = showDropdown === docId ? null : docId
  }

  // Close dropdown when clicking outside
  function handleClickOutside() {
    showDropdown = null
  }

  // Conversation status helpers
  function getConversationStatusIcon(status: string) {
    switch (status) {
      case 'active': return Play
      case 'paused': return Pause
      case 'completed': return CheckCircle
      case 'draft': return Clock
      default: return Clock
    }
  }

  function getConversationStatusColor(status: string) {
    switch (status) {
      case 'active': return 'bg-green-50 text-green-700 border-green-200'
      case 'paused': return 'bg-yellow-50 text-yellow-700 border-yellow-200'
      case 'completed': return 'bg-blue-50 text-blue-700 border-blue-200'
      case 'draft': return 'bg-gray-50 text-gray-700 border-gray-200'
      default: return 'bg-gray-50 text-gray-700 border-gray-200'
    }
  }

  function getConversationStatusLabel(status: string) {
    switch (status) {
      case 'active': return 'Active'
      case 'paused': return 'Paused'
      case 'completed': return 'Completed'
      case 'draft': return 'Draft'
      default: return 'Unknown'
    }
  }

  function formatLastActivity(dateString: string | null) {
    if (!dateString) return 'No activity'
    
    const now = new Date()
    const lastActivity = new Date(dateString)
    const diffInHours = Math.floor((now.getTime() - lastActivity.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) return 'Just now'
    if (diffInHours < 24) return `${diffInHours}h ago`
    
    const diffInDays = Math.floor(diffInHours / 24)
    if (diffInDays < 7) return `${diffInDays}d ago`
    
    const diffInWeeks = Math.floor(diffInDays / 7)
    return `${diffInWeeks}w ago`
  }

  function truncatePrompt(prompt: string | null, maxLength: number = 60) {
    if (!prompt) return 'No recent prompt'
    return prompt.length > maxLength ? prompt.substring(0, maxLength) + '...' : prompt
  }

  function handleContinueResearch(doc: any) {
    // Open the document and activate the agent panel
    dispatch('openDocument', doc)
    if (!rightPanelOpen) {
      dispatch('toggleRightPanel')
    }
  }

  function isConversationDocument(doc: any) {
    return doc.content_mode === 'accumulation'
  }
</script>

<svelte:window on:click={handleClickOutside} />

<div class="flex flex-col h-full bg-background">
  <!-- Header -->
  <div class="flex items-center justify-between p-6 border-b border-border bg-card">
    <div class="flex items-center gap-4">
      {#if !leftPanelOpen}
        <button
          on:click={toggleLeftPanel}
          class="p-2 hover:bg-muted rounded-lg transition-colors"
          title="Show sources panel"
        >
          <Menu class="w-5 h-5 text-foreground" />
        </button>
      {/if}
      
      <div>
        <h1 class="text-3xl font-bold text-foreground mb-2">Content Board</h1>
        <p class="text-muted-foreground">Manage and create your content with AI assistance</p>
      </div>
    </div>
    
    <div class="flex items-center gap-3">
      <!-- View Mode Toggle -->
      <div class="flex items-center border border-border rounded-lg p-1 bg-background">
        <button
          on:click={() => viewMode = 'grid'}
          class="p-1.5 rounded {viewMode === 'grid' ? 'bg-primary text-primary-foreground' : 'text-muted-foreground hover:text-foreground'} transition-colors"
        >
          <Grid3X3 class="w-4 h-4" />
        </button>
        <button
          on:click={() => viewMode = 'list'}
          class="p-1.5 rounded {viewMode === 'list' ? 'bg-primary text-primary-foreground' : 'text-muted-foreground hover:text-foreground'} transition-colors"
        >
          <List class="w-4 h-4" />
        </button>
      </div>

      <!-- Agent Toggle -->
      <button
        on:click={toggleRightPanel}
        class="flex items-center gap-2 px-4 py-2 border border-border bg-background hover:bg-accent transition-colors rounded-lg"
      >
        <MessageSquare class="w-4 h-4" />
        {rightPanelOpen ? 'Close Agent' : 'Open Agent'}
      </button>

      <!-- Create Button -->
      <button
        on:click={handleCreateDocument}
        class="flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground hover:bg-primary/90 transition-colors rounded-lg"
      >
        <Plus class="w-4 h-4" />
        New Document
      </button>
    </div>
  </div>

  <!-- Filters -->
  <div class="flex items-center gap-4 p-6 bg-card border-b border-border">
    <!-- Search -->
    <div class="relative flex-1 max-w-md">
      <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
      <input
        bind:value={searchTerm}
        on:input={handleSearch}
        placeholder="Search content..."
        class="w-full pl-9 pr-3 py-2 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary"
      />
    </div>

    <!-- Content Type Filter -->
    <select
      bind:value={selectedContentType}
      on:change={handleContentTypeChange}
      class="px-3 py-2 border border-border bg-background rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
    >
      {#each contentTypes as type}
        <option value={type.value}>{type.label}</option>
      {/each}
    </select>
  </div>

  <!-- Content Area -->
  <div class="flex-1 overflow-y-auto p-6">
    {#if selectedExternalSource && externalSourceData[selectedExternalSource]?.length > 0}
      <!-- External Source Content -->
      <div class="space-y-6">
        <div class="flex items-center justify-between">
          <h2 class="text-xl font-semibold text-foreground">{getExternalSourceTitle(selectedExternalSource)}</h2>
          <span class="text-sm text-muted-foreground">{externalSourceData[selectedExternalSource].length} items</span>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {#each externalSourceData[selectedExternalSource] as item}
            {#if selectedExternalSource === 'websites'}
              <div class="p-4 bg-card border border-border rounded-lg hover:shadow-sm transition-all duration-200">
                <div class="flex items-start gap-3">
                  <div class="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                  <div class="flex-1 min-w-0">
                    <h3 class="font-semibold text-foreground mb-2 line-clamp-2">{item.title}</h3>
                    <a href={item.url} target="_blank" class="text-sm text-blue-600 hover:underline mb-2 block truncate">
                      {item.url}
                    </a>
                    <p class="text-sm text-muted-foreground line-clamp-3 mb-3">{item.snippet}</p>
                    <div class="text-xs text-muted-foreground">
                      Crawled: {formatLastModified(item.lastCrawled)}
                    </div>
                  </div>
                </div>
              </div>
            {:else if selectedExternalSource === 'slack'}
              <div class="p-4 bg-card border border-border rounded-lg hover:shadow-sm transition-all duration-200">
                <div class="flex items-start gap-3">
                  <MessageSquare class="w-5 h-5 text-purple-500 mt-1 flex-shrink-0" />
                  <div class="flex-1 min-w-0">
                    <div class="flex items-center justify-between mb-2">
                      <h3 class="font-semibold text-foreground">{item.name}</h3>
                      <span class="text-xs text-muted-foreground">{item.members} members</span>
                    </div>
                    <p class="text-sm text-muted-foreground mb-2">"{item.lastMessage}"</p>
                    <div class="text-xs text-muted-foreground">
                      {item.lastActivity}
                    </div>
                  </div>
                </div>
              </div>
            {:else if selectedExternalSource === 'gdrive'}
              <div class="p-4 bg-card border border-border rounded-lg hover:shadow-sm transition-all duration-200">
                <div class="flex items-start gap-3">
                  <span class="text-2xl flex-shrink-0">{item.icon}</span>
                  <div class="flex-1 min-w-0">
                    <h3 class="font-semibold text-foreground mb-2 truncate">{item.name}</h3>
                    <div class="flex items-center justify-between text-sm text-muted-foreground">
                      <span>Modified: {formatLastModified(item.lastModified)}</span>
                      <span>{item.size}</span>
                    </div>
                  </div>
                </div>
              </div>
            {/if}
          {/each}
        </div>
      </div>
    {:else if isLoading}
      <!-- Loading State -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {#each Array(8) as _}
          <div class="h-48 bg-muted animate-pulse rounded-lg"></div>
        {/each}
      </div>
    {:else if documents.length === 0}
      <!-- Empty State -->
      <div class="flex flex-col items-center justify-center h-64 text-center">
        <FileText class="w-16 h-16 text-muted-foreground mb-4" />
        <h3 class="text-lg font-semibold text-foreground mb-2">No content found</h3>
        <p class="text-muted-foreground mb-6">
          {searchTerm ? 'Try adjusting your search terms' : 'Create your first document to get started'}
        </p>
        <button
          on:click={handleCreateDocument}
          class="flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground hover:bg-primary/90 transition-colors rounded-lg"
        >
          <Plus class="w-4 h-4" />
          Create Document
        </button>
      </div>
    {:else}
      <!-- Content Grid/List -->
      {#if viewMode === 'grid'}
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {#each documents as doc}
            <div class="group relative bg-card border border-border rounded-lg p-4 hover:shadow-md transition-all duration-200 cursor-pointer">
              <!-- Content Type Badge and Conversation Status -->
              <div class="flex items-center justify-between mb-3">
                <div class="flex items-center gap-2">
                  <span class="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium rounded-full border {getContentTypeColor(doc.content_type)}">
                    {getContentTypeIcon(doc.content_type)}
                    {contentTypes.find(t => t.value === doc.content_type)?.label || doc.content_type}
                  </span>
                  
                  {#if isConversationDocument(doc)}
                    <!-- Conversation Status Badge -->
                    {#if doc.conversation_status}
                      <span class="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium rounded-full border {getConversationStatusColor(doc.conversation_status)}">
                        <svelte:component this={getConversationStatusIcon(doc.conversation_status)} class="w-3 h-3" />
                        {getConversationStatusLabel(doc.conversation_status)}
                      </span>
                    {/if}
                  {/if}
                </div>
                
                <!-- Actions Menu -->
                <div class="relative">
                  <button
                    on:click|stopPropagation={() => toggleDropdown(doc.id)}
                    class="p-1 opacity-0 group-hover:opacity-100 hover:bg-muted rounded transition-all"
                  >
                    <MoreVertical class="w-4 h-4" />
                  </button>
                  
                  {#if showDropdown === doc.id}
                    <div class="absolute right-0 top-8 w-32 bg-popover border border-border rounded-lg shadow-lg z-10">
                      <button
                        on:click|stopPropagation={() => handleDeleteDocument(doc.id)}
                        class="flex items-center gap-2 w-full px-3 py-2 text-sm text-destructive hover:bg-muted rounded-lg"
                      >
                        <Trash2 class="w-3 h-3" />
                        Delete
                      </button>
                    </div>
                  {/if}
                </div>
              </div>

              <!-- Content -->
              <div on:click={() => handleOpenDocument(doc)} class="cursor-pointer">
                <h3 class="font-semibold text-foreground mb-2 line-clamp-2">
                  {doc.title}
                </h3>
                
                {#if isConversationDocument(doc)}
                  <!-- Conversation Metadata -->
                  <div class="space-y-2 mb-3">
                    {#if doc.conversation_count > 0}
                      <div class="flex items-center gap-1 text-xs text-muted-foreground">
                        <MessageSquare class="w-3 h-3" />
                        <span>{doc.conversation_count} interaction{doc.conversation_count !== 1 ? 's' : ''}</span>
                        {#if doc.last_activity_at}
                          <span>•</span>
                          <span>{formatLastActivity(doc.last_activity_at)}</span>
                        {/if}
                      </div>
                    {/if}
                    
                    {#if doc.last_prompt}
                      <div class="text-xs text-muted-foreground italic">
                        "{truncatePrompt(doc.last_prompt, 80)}"
                      </div>
                    {/if}
                    
                    {#if doc.tags && doc.tags.length > 0}
                      <div class="flex flex-wrap gap-1">
                        {#each doc.tags.slice(0, 3) as tag}
                          <span class="inline-flex items-center gap-1 px-1.5 py-0.5 text-xs bg-muted text-muted-foreground rounded">
                            <Hash class="w-2 h-2" />
                            {tag}
                          </span>
                        {/each}
                        {#if doc.tags.length > 3}
                          <span class="text-xs text-muted-foreground">+{doc.tags.length - 3}</span>
                        {/if}
                      </div>
                    {/if}
                  </div>
                {:else}
                  <!-- Traditional Content Preview -->
                  <div class="text-sm text-muted-foreground mb-4 line-clamp-3">
                    {#if doc.content}
                      {doc.content.replace(/<[^>]*>/g, '').substring(0, 120)}...
                    {:else}
                      No content yet
                    {/if}
                  </div>
                {/if}

                <!-- Metadata -->
                <div class="flex items-center justify-between text-xs text-muted-foreground">
                  <span>{formatDate(doc.updated_at)}</span>
                  {#if doc.reading_time}
                    <span>{formatReadingTime(doc.reading_time)}</span>
                  {/if}
                </div>
              </div>
              
              <!-- Continue Research Button for Conversations -->
              {#if isConversationDocument(doc) && (doc.conversation_status === 'active' || doc.conversation_status === 'paused')}
                <button
                  on:click|stopPropagation={() => handleContinueResearch(doc)}
                  class="mt-3 w-full flex items-center justify-center gap-2 px-3 py-2 text-xs bg-primary/10 text-primary hover:bg-primary/20 transition-colors rounded-lg opacity-0 group-hover:opacity-100"
                >
                  <RefreshCw class="w-3 h-3" />
                  Continue Research
                </button>
              {/if}
            </div>
          {/each}
        </div>
      {:else}
        <!-- List View -->
        <div class="space-y-2">
          {#each documents as doc}
            <div class="group flex items-center gap-4 p-4 bg-card border border-border rounded-lg hover:shadow-sm transition-all duration-200">
              <span class="text-2xl">{getContentTypeIcon(doc.content_type)}</span>
              
              <div class="flex-1 min-w-0 cursor-pointer" on:click={() => handleOpenDocument(doc)}>
                <div class="flex items-center gap-2 mb-1">
                  <h3 class="font-semibold text-foreground truncate">{doc.title}</h3>
                  <span class="inline-flex items-center px-2 py-0.5 text-xs font-medium rounded-full border {getContentTypeColor(doc.content_type)}">
                    {contentTypes.find(t => t.value === doc.content_type)?.label || doc.content_type}
                  </span>
                  
                  {#if isConversationDocument(doc) && doc.conversation_status}
                    <span class="inline-flex items-center gap-1 px-2 py-0.5 text-xs font-medium rounded-full border {getConversationStatusColor(doc.conversation_status)}">
                      <svelte:component this={getConversationStatusIcon(doc.conversation_status)} class="w-3 h-3" />
                      {getConversationStatusLabel(doc.conversation_status)}
                    </span>
                  {/if}
                </div>
                
                {#if isConversationDocument(doc)}
                  <div class="flex items-center gap-4 text-sm text-muted-foreground mb-1">
                    {#if doc.conversation_count > 0}
                      <div class="flex items-center gap-1">
                        <MessageSquare class="w-3 h-3" />
                        <span>{doc.conversation_count} interaction{doc.conversation_count !== 1 ? 's' : ''}</span>
                      </div>
                    {/if}
                    
                    {#if doc.last_activity_at}
                      <span>{formatLastActivity(doc.last_activity_at)}</span>
                    {/if}
                  </div>
                  
                  {#if doc.last_prompt}
                    <p class="text-sm text-muted-foreground italic truncate">
                      "{truncatePrompt(doc.last_prompt, 120)}"
                    </p>
                  {/if}
                  
                  {#if doc.tags && doc.tags.length > 0}
                    <div class="flex items-center gap-1 mt-1">
                      {#each doc.tags.slice(0, 4) as tag}
                        <span class="inline-flex items-center gap-1 px-1.5 py-0.5 text-xs bg-muted text-muted-foreground rounded">
                          <Hash class="w-2 h-2" />
                          {tag}
                        </span>
                      {/each}
                      {#if doc.tags.length > 4}
                        <span class="text-xs text-muted-foreground">+{doc.tags.length - 4}</span>
                      {/if}
                    </div>
                  {/if}
                {:else}
                  <p class="text-sm text-muted-foreground truncate">
                    {doc.content ? doc.content.replace(/<[^>]*>/g, '').substring(0, 100) + '...' : 'No content yet'}
                  </p>
                {/if}
              </div>

              <div class="flex items-center gap-4 text-sm text-muted-foreground">
                <span>{formatDate(doc.updated_at)}</span>
                {#if doc.reading_time}
                  <span>{formatReadingTime(doc.reading_time)}</span>
                {/if}
                
                {#if isConversationDocument(doc) && (doc.conversation_status === 'active' || doc.conversation_status === 'paused')}
                  <button
                    on:click|stopPropagation={() => handleContinueResearch(doc)}
                    class="flex items-center gap-1 px-2 py-1 text-xs bg-primary/10 text-primary hover:bg-primary/20 transition-colors rounded opacity-0 group-hover:opacity-100"
                  >
                    <RefreshCw class="w-3 h-3" />
                    Continue
                  </button>
                {/if}
                
                <div class="relative">
                  <button
                    on:click|stopPropagation={() => toggleDropdown(doc.id)}
                    class="p-1 opacity-0 group-hover:opacity-100 hover:bg-muted rounded transition-all"
                  >
                    <MoreVertical class="w-4 h-4" />
                  </button>
                  
                  {#if showDropdown === doc.id}
                    <div class="absolute right-0 top-8 w-32 bg-popover border border-border rounded-lg shadow-lg z-10">
                      <button
                        on:click|stopPropagation={() => handleDeleteDocument(doc.id)}
                        class="flex items-center gap-2 w-full px-3 py-2 text-sm text-destructive hover:bg-muted rounded-lg"
                      >
                        <Trash2 class="w-3 h-3" />
                        Delete
                      </button>
                    </div>
                  {/if}
                </div>
              </div>
            </div>
          {/each}
        </div>
      {/if}
    {/if}
  </div>
</div>
