<script lang="ts">
  import { createEventDispatcher } from "svelte"
  import ContentSourcesPanel from "./ContentSourcesPanel.svelte"
  import ContentDisplayArea from "./ContentDisplayArea.svelte"
  import EnhancedAgentSidebar from "./EnhancedAgentSidebar.svelte"
  import ContentCanvas from "./ContentCanvas.svelte"

  export let currentDocument: any = null
  export let documents: any[] = []
  export let isLoading: boolean = false
  export let searchTerm: string = ""
  export let selectedContentType: string = "all"
  export let showDocumentList: boolean = true
  export let selectedText: string = ""
  export let contentTypes: any[] = []
  export let session: any
  export let environment: any
  export let contentService: any
  export let canvasComponent: any

  const dispatch = createEventDispatcher()
  
  // Generate conversation ID for this session
  let conversationId: string = crypto.randomUUID()

  // Panel visibility state
  let leftPanelOpen = true
  let rightPanelOpen = false

  // External sources state
  let selectedExternalSource: string | null = null
  let externalSourceData: any = {
    websites: [],
    slack: [],
    gdrive: []
  }

  function toggleLeftPanel() {
    leftPanelOpen = !leftPanelOpen
  }

  function toggleRightPanel() {
    rightPanelOpen = !rightPanelOpen
  }

  function handleCreateDocument() {
    dispatch('createDocument')
  }

  function handleOpenDocument(event: CustomEvent) {
    dispatch('openDocument', event.detail)
  }

  function handleSaveDocument(event: CustomEvent) {
    dispatch('saveDocument', event.detail)
  }

  function handleDeleteDocument(event: CustomEvent) {
    dispatch('deleteDocument', event.detail)
  }

  function handleSelectionChange(event: CustomEvent) {
    dispatch('selectionChange', event.detail)
  }

  function handleContentGenerated(event: CustomEvent) {
    dispatch('contentGenerated', event.detail)
  }

  function handleReplaceContent(event: CustomEvent) {
    dispatch('replaceContent', event.detail)
  }

  function handleContentAppended(event: CustomEvent) {
    const { content, section } = event.detail
    
    console.log('Content appended in layout:', { 
      hasContent: !!content, 
      hasDocument: !!currentDocument,
      documentId: currentDocument?.id,
      contentLength: content?.length,
      sectionTitle: section?.section_title
    })
    
    // Update the current document with the new markdown content
    if (currentDocument) {
      const previousMode = currentDocument.content_mode
      currentDocument.markdown_content = content
      currentDocument.content_mode = 'accumulation'
      
      // Force reactivity update
      currentDocument = { ...currentDocument }
      
      console.log('Document updated:', {
        documentId: currentDocument.id,
        previousMode,
        newMode: currentDocument.content_mode,
        markdownContentLength: currentDocument.markdown_content?.length
      })
    }
    
    dispatch('contentAppended', event.detail)
  }

  function handleGoBackToList() {
    dispatch('goBackToList')
  }

  function handleSearchDocuments() {
    dispatch('searchDocuments')
  }

  function handleLoadDocuments() {
    dispatch('loadDocuments')
  }

  function handleExternalSourceConnected(event: CustomEvent) {
    const { sourceId, data } = event.detail
    externalSourceData[sourceId] = data
    selectedExternalSource = sourceId
  }

  function handleExternalSourceSelected(event: CustomEvent) {
    selectedExternalSource = event.detail.sourceId
  }
</script>

<div class="flex h-full bg-background">
  <!-- Left Panel - Content Sources -->
  {#if leftPanelOpen}
    <div class="w-64 border-r border-border bg-card flex-shrink-0 transition-all duration-300">
      <ContentSourcesPanel
        {documents}
        {contentTypes}
        {selectedContentType}
        {searchTerm}
        {isLoading}
        on:createDocument={handleCreateDocument}
        on:openDocument={handleOpenDocument}
        on:deleteDocument={handleDeleteDocument}
        on:searchDocuments={handleSearchDocuments}
        on:loadDocuments={handleLoadDocuments}
        on:externalSourceConnected={handleExternalSourceConnected}
        on:externalSourceSelected={handleExternalSourceSelected}
      />
    </div>
  {/if}

  <!-- Main Content Area -->
  <div class="flex-1 flex flex-col min-w-0">
    {#if showDocumentList}
      <!-- Content Display Area -->
      <ContentDisplayArea
        {documents}
        {contentTypes}
        {selectedContentType}
        {searchTerm}
        {isLoading}
        {leftPanelOpen}
        {rightPanelOpen}
        {selectedExternalSource}
        {externalSourceData}
        on:createDocument={handleCreateDocument}
        on:openDocument={handleOpenDocument}
        on:deleteDocument={handleDeleteDocument}
        on:searchDocuments={handleSearchDocuments}
        on:loadDocuments={handleLoadDocuments}
        on:toggleLeftPanel={toggleLeftPanel}
        on:toggleRightPanel={toggleRightPanel}
      />
    {:else}
      <!-- Document Editor View -->
      <div class="flex h-full">
        <!-- Editor Area -->
        <div class="flex-1 flex flex-col">
          <!-- Editor Header -->
          <div class="flex items-center justify-between p-4 border-b border-border bg-card">
            <div class="flex items-center gap-3">
              <button
                on:click={handleGoBackToList}
                class="flex items-center gap-2 px-3 py-1.5 text-sm text-muted-foreground hover:text-foreground transition-colors"
              >
                ← Back to Content Board
              </button>
              <div class="w-px h-4 bg-border"></div>
              <h1 class="text-lg font-semibold text-foreground">
                {currentDocument?.title || 'Untitled Document'}
              </h1>
            </div>
            
            <div class="flex items-center gap-2">
              {#if !leftPanelOpen}
                <button
                  on:click={toggleLeftPanel}
                  class="p-2 hover:bg-muted rounded-lg transition-colors"
                  title="Show sources panel"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                  </svg>
                </button>
              {/if}
              
              <button
                on:click={toggleRightPanel}
                class="flex items-center gap-2 px-3 py-1.5 text-sm border border-border bg-background hover:bg-accent transition-colors rounded-lg"
              >
                {#if rightPanelOpen}
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 5l7 7-7 7M5 5l7 7-7 7" />
                  </svg>
                  Close Agent
                {:else}
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                  Open Agent
                {/if}
              </button>
            </div>
          </div>

          <!-- Content Canvas -->
          <ContentCanvas
            bind:this={canvasComponent}
            content={currentDocument?.content || ""}
            markdownContent={currentDocument?.markdown_content || ""}
            contentMode={currentDocument?.content_mode || "single"}
            title={currentDocument?.title || ""}
            documentId={currentDocument?.id}
            envSlug={environment?.slug}
            conversationSections={[]}
            on:save={handleSaveDocument}
            on:contentChange={(e) => {
              if (currentDocument) {
                currentDocument.content = e.detail.content
              }
            }}
            on:titleChange={(e) => {
              if (currentDocument) {
                currentDocument.title = e.detail.title
              }
            }}
            on:selectionChange={handleSelectionChange}
            {isLoading}
          />
        </div>
      </div>
    {/if}
  </div>

  <!-- Right Panel - Enhanced Agent Sidebar -->
  {#if rightPanelOpen}
    <div class="w-96 border-l border-border bg-card flex-shrink-0 transition-all duration-300">
      <EnhancedAgentSidebar
        documentId={currentDocument?.id}
        currentContent={currentDocument?.content || ""}
        {selectedText}
        {conversationId}
        envSlug={environment?.slug}
        on:close={toggleRightPanel}
        on:contentGenerated={handleContentGenerated}
        on:replaceContent={handleReplaceContent}
        on:contentAppended={handleContentAppended}
      />
    </div>
  {/if}
</div>
