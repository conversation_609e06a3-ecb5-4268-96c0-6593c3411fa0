import type { SupabaseClient } from '@supabase/supabase-js'
import type { Database } from '$lib/supabase/supabase.types'

type ContentDocument = Database['public']['Tables']['content_documents']['Row']
type ContentDocumentInsert = Database['public']['Tables']['content_documents']['Insert']
type ContentDocumentUpdate = Database['public']['Tables']['content_documents']['Update']

type ContentSession = Database['public']['Tables']['content_sessions']['Row']
type ContentSessionInsert = Database['public']['Tables']['content_sessions']['Insert']
type ContentSessionUpdate = Database['public']['Tables']['content_sessions']['Update']

type ContentCitation = Database['public']['Tables']['content_citations']['Row']
type ContentCitationInsert = Database['public']['Tables']['content_citations']['Insert']

type ContentSection = Database['public']['Tables']['content_sections']['Row']
type ContentSectionInsert = Database['public']['Tables']['content_sections']['Insert']
type ContentSectionUpdate = Database['public']['Tables']['content_sections']['Update']

export class ContentService {
  constructor(private supabase: SupabaseClient<Database>) {}

  // Document Operations
  async createDocument(data: ContentDocumentInsert): Promise<ContentDocument | null> {
    const { data: document, error } = await this.supabase
      .from('content_documents')
      .insert(data)
      .select()
      .single()

    if (error) {
      console.error('Error creating document:', error)
      throw new Error(`Failed to create document: ${error.message}`)
    }

    return document
  }

  async getDocument(documentId: string, userId: string): Promise<ContentDocument | null> {
    const { data: document, error } = await this.supabase
      .from('content_documents')
      .select('*')
      .eq('id', documentId)
      .eq('user_id', userId)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null // Document not found
      }
      console.error('Error fetching document:', error)
      throw new Error(`Failed to fetch document: ${error.message}`)
    }

    return document
  }

  async updateDocument(
    documentId: string,
    userId: string,
    updates: ContentDocumentUpdate
  ): Promise<ContentDocument | null> {
    const { data: document, error } = await this.supabase
      .from('content_documents')
      .update(updates)
      .eq('id', documentId)
      .eq('user_id', userId)
      .select()
      .single()

    if (error) {
      console.error('Error updating document:', error)
      throw new Error(`Failed to update document: ${error.message}`)
    }

    return document
  }

  async deleteDocument(documentId: string, userId: string): Promise<boolean> {
    const { error } = await this.supabase
      .from('content_documents')
      .delete()
      .eq('id', documentId)
      .eq('user_id', userId)

    if (error) {
      console.error('Error deleting document:', error)
      throw new Error(`Failed to delete document: ${error.message}`)
    }

    return true
  }

  async getUserDocuments(
    userId: string,
    environmentId: string,
    options: {
      limit?: number
      offset?: number
      status?: string
      contentType?: string
      orderBy?: 'created_at' | 'updated_at' | 'title'
      orderDirection?: 'asc' | 'desc'
    } = {}
  ): Promise<ContentDocument[]> {
    const {
      limit = 50,
      offset = 0,
      status,
      contentType,
      orderBy = 'updated_at',
      orderDirection = 'desc'
    } = options

    let query = this.supabase
      .from('content_documents')
      .select('*')
      .eq('user_id', userId)
      .eq('environment_id', environmentId)

    if (status) {
      query = query.eq('status', status)
    }

    if (contentType) {
      query = query.eq('content_type', contentType)
    }

    query = query
      .order(orderBy, { ascending: orderDirection === 'asc' })
      .range(offset, offset + limit - 1)

    const { data: documents, error } = await query

    if (error) {
      console.error('Error fetching user documents:', error)
      throw new Error(`Failed to fetch documents: ${error.message}`)
    }

    return documents || []
  }

  async searchDocuments(
    userId: string,
    environmentId: string,
    searchTerm: string,
    options: {
      limit?: number
      contentType?: string
    } = {}
  ): Promise<ContentDocument[]> {
    const { limit = 20, contentType } = options

    let query = this.supabase
      .from('content_documents')
      .select('*')
      .eq('user_id', userId)
      .eq('environment_id', environmentId)
      .or(`title.ilike.%${searchTerm}%,content->>'text'.ilike.%${searchTerm}%`)

    if (contentType) {
      query = query.eq('content_type', contentType)
    }

    query = query
      .order('updated_at', { ascending: false })
      .limit(limit)

    const { data: documents, error } = await query

    if (error) {
      console.error('Error searching documents:', error)
      throw new Error(`Failed to search documents: ${error.message}`)
    }

    return documents || []
  }

  // Session Operations
  async createSession(data: ContentSessionInsert): Promise<ContentSession | null> {
    const { data: session, error } = await this.supabase
      .from('content_sessions')
      .insert(data)
      .select()
      .single()

    if (error) {
      console.error('Error creating session:', error)
      throw new Error(`Failed to create session: ${error.message}`)
    }

    return session
  }

  async getSession(sessionId: string, userId: string): Promise<ContentSession | null> {
    const { data: session, error } = await this.supabase
      .from('content_sessions')
      .select('*')
      .eq('id', sessionId)
      .eq('user_id', userId)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null
      }
      console.error('Error fetching session:', error)
      throw new Error(`Failed to fetch session: ${error.message}`)
    }

    return session
  }

  async updateSession(
    sessionId: string,
    userId: string,
    updates: ContentSessionUpdate
  ): Promise<ContentSession | null> {
    const { data: session, error } = await this.supabase
      .from('content_sessions')
      .update(updates)
      .eq('id', sessionId)
      .eq('user_id', userId)
      .select()
      .single()

    if (error) {
      console.error('Error updating session:', error)
      throw new Error(`Failed to update session: ${error.message}`)
    }

    return session
  }

  async getDocumentSessions(documentId: string, userId: string): Promise<ContentSession[]> {
    const { data: sessions, error } = await this.supabase
      .from('content_sessions')
      .select('*')
      .eq('document_id', documentId)
      .eq('user_id', userId)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching document sessions:', error)
      throw new Error(`Failed to fetch sessions: ${error.message}`)
    }

    return sessions || []
  }

  // Citation Operations
  async addCitation(data: ContentCitationInsert): Promise<ContentCitation | null> {
    const { data: citation, error } = await this.supabase
      .from('content_citations')
      .insert(data)
      .select()
      .single()

    if (error) {
      console.error('Error adding citation:', error)
      throw new Error(`Failed to add citation: ${error.message}`)
    }

    return citation
  }

  async getDocumentCitations(documentId: string): Promise<ContentCitation[]> {
    const { data: citations, error } = await this.supabase
      .from('content_citations')
      .select('*')
      .eq('document_id', documentId)
      .order('position_in_content', { ascending: true })

    if (error) {
      console.error('Error fetching citations:', error)
      throw new Error(`Failed to fetch citations: ${error.message}`)
    }

    return citations || []
  }

  async updateCitation(
    citationId: string,
    updates: Partial<ContentCitationInsert>
  ): Promise<ContentCitation | null> {
    const { data: citation, error } = await this.supabase
      .from('content_citations')
      .update(updates)
      .eq('id', citationId)
      .select()
      .single()

    if (error) {
      console.error('Error updating citation:', error)
      throw new Error(`Failed to update citation: ${error.message}`)
    }

    return citation
  }

  async deleteCitation(citationId: string): Promise<boolean> {
    const { error } = await this.supabase
      .from('content_citations')
      .delete()
      .eq('id', citationId)

    if (error) {
      console.error('Error deleting citation:', error)
      throw new Error(`Failed to delete citation: ${error.message}`)
    }

    return true
  }

  // Content Section Operations
  async addContentSection(data: ContentSectionInsert): Promise<ContentSection | null> {
    const { data: section, error } = await this.supabase
      .from('content_sections')
      .insert(data)
      .select()
      .single()

    if (error) {
      console.error('Error adding content section:', error)
      throw new Error(`Failed to add content section: ${error.message}`)
    }

    return section
  }

  async getDocumentSections(documentId: string, userId: string): Promise<ContentSection[]> {
    const { data: sections, error } = await this.supabase
      .from('content_sections')
      .select('*')
      .eq('document_id', documentId)
      .eq('user_id', userId)
      .order('order_index', { ascending: true })
      .order('created_at', { ascending: true })

    if (error) {
      console.error('Error fetching document sections:', error)
      throw new Error(`Failed to fetch document sections: ${error.message}`)
    }

    return sections || []
  }

  async updateContentSection(
    sectionId: string,
    userId: string,
    updates: ContentSectionUpdate
  ): Promise<ContentSection | null> {
    const { data: section, error } = await this.supabase
      .from('content_sections')
      .update(updates)
      .eq('id', sectionId)
      .eq('user_id', userId)
      .select()
      .single()

    if (error) {
      console.error('Error updating content section:', error)
      throw new Error(`Failed to update content section: ${error.message}`)
    }

    return section
  }

  async deleteContentSection(sectionId: string, userId: string): Promise<boolean> {
    const { error } = await this.supabase
      .from('content_sections')
      .delete()
      .eq('id', sectionId)
      .eq('user_id', userId)

    if (error) {
      console.error('Error deleting content section:', error)
      throw new Error(`Failed to delete content section: ${error.message}`)
    }

    return true
  }

  // Markdown Content Operations
  async appendContentToDocument(
    documentId: string,
    userId: string,
    content: string,
    agentSource: string = 'content-agent',
    originalPrompt?: string
  ): Promise<{ section: ContentSection; markdownContent: string } | null> {
    try {
      // First, get the current document to check its content mode
      const document = await this.getDocument(documentId, userId)
      if (!document) {
        throw new Error('Document not found')
      }

      // If this is the first content addition, switch to accumulation mode
      if (document.content_mode === 'single') {
        await this.updateDocument(documentId, userId, {
          content_mode: 'accumulation'
        })
      }

      // Get the next order index
      const sections = await this.getDocumentSections(documentId, userId)
      const nextOrderIndex = sections.length

      // Generate section title based on timestamp and content type
      const timestamp = new Date().toLocaleString()
      const sectionTitle = `Section ${nextOrderIndex + 1} - ${timestamp}`

      // Add the new section
      const sectionData: ContentSectionInsert = {
        document_id: documentId,
        user_id: userId,
        section_title: sectionTitle,
        section_content: content,
        agent_source: agentSource,
        original_prompt: originalPrompt,
        order_index: nextOrderIndex,
        metadata: {}
      }

      const section = await this.addContentSection(sectionData)
      if (!section) {
        throw new Error('Failed to add content section')
      }

      // Regenerate markdown content
      const { data: markdownData, error: markdownError } = await this.supabase
        .rpc('regenerate_markdown_content', { doc_id: documentId })

      if (markdownError) {
        console.error('Error regenerating markdown:', markdownError)
        throw new Error(`Failed to regenerate markdown: ${markdownError.message}`)
      }

      const markdownContent = markdownData as string

      // Update the document with the new markdown content
      await this.updateDocument(documentId, userId, {
        markdown_content: markdownContent
      })

      return { section, markdownContent }
    } catch (error) {
      console.error('Error appending content to document:', error)
      throw error
    }
  }

  async switchDocumentMode(
    documentId: string,
    userId: string,
    mode: 'single' | 'accumulation'
  ): Promise<ContentDocument | null> {
    const document = await this.getDocument(documentId, userId)
    if (!document) {
      throw new Error('Document not found')
    }

    if (mode === 'single' && document.content_mode === 'accumulation') {
      // Convert accumulated content back to single mode
      // This will flatten all sections into the main content field
      const sections = await this.getDocumentSections(documentId, userId)
      let combinedContent = ''
      
      sections.forEach((section, index) => {
        if (index > 0) combinedContent += '\n\n'
        combinedContent += section.section_content
      })

      return await this.updateDocument(documentId, userId, {
        content_mode: mode,
        content: { text: combinedContent },
        markdown_content: null
      })
    } else if (mode === 'accumulation' && document.content_mode === 'single') {
      // Convert single content to accumulation mode
      const existingContent = typeof document.content === 'object' && document.content && 'text' in document.content 
        ? (document.content as any).text 
        : document.content?.toString() || ''

      if (existingContent.trim()) {
        // Create first section from existing content
        const sectionData: ContentSectionInsert = {
          document_id: documentId,
          user_id: userId,
          section_title: 'Original Content',
          section_content: existingContent,
          agent_source: 'manual',
          order_index: 0,
          metadata: {}
        }

        await this.addContentSection(sectionData)

        // Regenerate markdown content
        const { data: markdownData } = await this.supabase
          .rpc('regenerate_markdown_content', { doc_id: documentId })

        return await this.updateDocument(documentId, userId, {
          content_mode: mode,
          markdown_content: markdownData as string
        })
      } else {
        return await this.updateDocument(documentId, userId, {
          content_mode: mode
        })
      }
    }

    return document
  }

  async regenerateMarkdownContent(documentId: string): Promise<string | null> {
    const { data, error } = await this.supabase
      .rpc('regenerate_markdown_content', { doc_id: documentId })

    if (error) {
      console.error('Error regenerating markdown content:', error)
      throw new Error(`Failed to regenerate markdown content: ${error.message}`)
    }

    return data as string
  }

  async exportDocumentAsMarkdown(documentId: string, userId: string, includeMetadata: boolean = true): Promise<string> {
    const document = await this.getDocument(documentId, userId)
    if (!document) {
      throw new Error('Document not found')
    }

    if (document.content_mode === 'accumulation' && document.markdown_content) {
      if (!includeMetadata) {
        // Strip metadata lines (lines starting with * and section headers with timestamps)
        return document.markdown_content
          .split('\n')
          .filter(line => !line.trim().startsWith('*Prompt:') && !line.includes('Generated by'))
          .join('\n')
          .replace(/## Section \d+ - [\d-]+ [\d:]+ - /g, '## ')
      }
      return document.markdown_content
    } else {
      // Single mode - convert content to markdown
      const content = typeof document.content === 'object' && document.content && 'text' in document.content 
        ? (document.content as any).text 
        : document.content?.toString() || ''
      
      return `# ${document.title}\n\n${content}`
    }
  }

  // Conversation Metadata Operations
  async updateConversationMetadata(
    documentId: string,
    userId: string,
    metadata: {
      conversation_status?: 'draft' | 'active' | 'paused' | 'completed'
      last_prompt?: string
      tags?: string[]
    }
  ): Promise<ContentDocument | null> {
    const updates: ContentDocumentUpdate = {
      ...metadata,
      last_activity_at: new Date().toISOString()
    }

    return await this.updateDocument(documentId, userId, updates)
  }

  async getConversationDocuments(
    userId: string,
    environmentId: string,
    conversationStatus?: 'draft' | 'active' | 'paused' | 'completed',
    options: {
      limit?: number
      offset?: number
      orderBy?: 'last_activity_at' | 'conversation_count' | 'created_at'
      orderDirection?: 'asc' | 'desc'
    } = {}
  ): Promise<ContentDocument[]> {
    const {
      limit = 50,
      offset = 0,
      orderBy = 'last_activity_at',
      orderDirection = 'desc'
    } = options

    let query = this.supabase
      .from('content_documents')
      .select('*')
      .eq('user_id', userId)
      .eq('environment_id', environmentId)
      .eq('content_mode', 'accumulation') // Only accumulation mode documents have conversations

    if (conversationStatus) {
      query = query.eq('conversation_status', conversationStatus)
    }

    query = query
      .order(orderBy, { ascending: orderDirection === 'asc' })
      .range(offset, offset + limit - 1)

    const { data: documents, error } = await query

    if (error) {
      console.error('Error fetching conversation documents:', error)
      throw new Error(`Failed to fetch conversation documents: ${error.message}`)
    }

    return documents || []
  }

  async pauseConversation(documentId: string, userId: string): Promise<ContentDocument | null> {
    return await this.updateConversationMetadata(documentId, userId, {
      conversation_status: 'paused'
    })
  }

  async resumeConversation(documentId: string, userId: string): Promise<ContentDocument | null> {
    return await this.updateConversationMetadata(documentId, userId, {
      conversation_status: 'active'
    })
  }

  async completeConversation(documentId: string, userId: string): Promise<ContentDocument | null> {
    return await this.updateConversationMetadata(documentId, userId, {
      conversation_status: 'completed'
    })
  }

  async addConversationTag(
    documentId: string,
    userId: string,
    tag: string
  ): Promise<ContentDocument | null> {
    const document = await this.getDocument(documentId, userId)
    if (!document) {
      throw new Error('Document not found')
    }

    const currentTags = document.tags || []
    if (!currentTags.includes(tag)) {
      const updatedTags = [...currentTags, tag]
      return await this.updateConversationMetadata(documentId, userId, {
        tags: updatedTags
      })
    }

    return document
  }

  async removeConversationTag(
    documentId: string,
    userId: string,
    tag: string
  ): Promise<ContentDocument | null> {
    const document = await this.getDocument(documentId, userId)
    if (!document) {
      throw new Error('Document not found')
    }

    const currentTags = document.tags || []
    const updatedTags = currentTags.filter(t => t !== tag)
    
    return await this.updateConversationMetadata(documentId, userId, {
      tags: updatedTags
    })
  }

  async getActiveConversations(
    userId: string,
    environmentId: string,
    limit: number = 10
  ): Promise<ContentDocument[]> {
    return await this.getConversationDocuments(userId, environmentId, 'active', {
      limit,
      orderBy: 'last_activity_at',
      orderDirection: 'desc'
    })
  }

  async getRecentConversations(
    userId: string,
    environmentId: string,
    limit: number = 10
  ): Promise<ContentDocument[]> {
    return await this.getConversationDocuments(userId, environmentId, undefined, {
      limit,
      orderBy: 'last_activity_at',
      orderDirection: 'desc'
    })
  }

  async searchConversationsByTag(
    userId: string,
    environmentId: string,
    tag: string,
    limit: number = 20
  ): Promise<ContentDocument[]> {
    const { data: documents, error } = await this.supabase
      .from('content_documents')
      .select('*')
      .eq('user_id', userId)
      .eq('environment_id', environmentId)
      .eq('content_mode', 'accumulation')
      .contains('tags', [tag])
      .order('last_activity_at', { ascending: false })
      .limit(limit)

    if (error) {
      console.error('Error searching conversations by tag:', error)
      throw new Error(`Failed to search conversations by tag: ${error.message}`)
    }

    return documents || []
  }

  async getConversationStats(userId: string, environmentId: string) {
    const { data, error } = await this.supabase
      .from('content_documents')
      .select('conversation_status, conversation_count, tags, last_activity_at')
      .eq('user_id', userId)
      .eq('environment_id', environmentId)
      .eq('content_mode', 'accumulation')

    if (error) {
      console.error('Error fetching conversation stats:', error)
      throw new Error(`Failed to fetch conversation stats: ${error.message}`)
    }

    const now = new Date()
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000)
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)

    const stats = {
      total: data?.length || 0,
      byStatus: {} as Record<string, number>,
      totalInteractions: 0,
      recentActivity: {
        lastDay: 0,
        lastWeek: 0
      },
      popularTags: {} as Record<string, number>
    }

    data?.forEach(doc => {
      // Count by status
      const status = doc.conversation_status || 'draft'
      stats.byStatus[status] = (stats.byStatus[status] || 0) + 1
      
      // Total interactions
      stats.totalInteractions += doc.conversation_count || 0
      
      // Recent activity
      if (doc.last_activity_at) {
        const lastActivity = new Date(doc.last_activity_at)
        if (lastActivity > oneDayAgo) {
          stats.recentActivity.lastDay++
        }
        if (lastActivity > oneWeekAgo) {
          stats.recentActivity.lastWeek++
        }
      }
      
      // Popular tags
      doc.tags?.forEach(tag => {
        stats.popularTags[tag] = (stats.popularTags[tag] || 0) + 1
      })
    })

    return stats
  }

  // Utility Methods
  async getDocumentStats(userId: string, environmentId: string) {
    const { data, error } = await this.supabase
      .from('content_documents')
      .select('status, content_type, content_mode, section_count')
      .eq('user_id', userId)
      .eq('environment_id', environmentId)

    if (error) {
      console.error('Error fetching document stats:', error)
      throw new Error(`Failed to fetch document stats: ${error.message}`)
    }

    const stats = {
      total: data?.length || 0,
      byStatus: {} as Record<string, number>,
      byType: {} as Record<string, number>,
      byMode: {} as Record<string, number>,
      totalSections: 0
    }

    data?.forEach(doc => {
      stats.byStatus[doc.status] = (stats.byStatus[doc.status] || 0) + 1
      stats.byType[doc.content_type] = (stats.byType[doc.content_type] || 0) + 1
      stats.byMode[doc.content_mode || 'single'] = (stats.byMode[doc.content_mode || 'single'] || 0) + 1
      stats.totalSections += doc.section_count || 0
    })

    return stats
  }
}
