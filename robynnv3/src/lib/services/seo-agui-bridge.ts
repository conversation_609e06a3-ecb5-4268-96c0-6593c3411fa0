// SEO AG-UI Bridge Service
// Bridges between AG-UI stores and existing SEO stores for seamless integration

import { get } from 'svelte/store'
import type { 
  SEOMessage, 
  ProgressStep, 
  NicheKeyword, 
  GapKeyword 
} from '$lib/stores/seo-agent-store'
import {
  messages as seoMessages,
  progressSteps as seoProgressSteps,
  nicheKeywords as seoNicheKeywords,
  gapKeywords as seoGapKeywords,
  isLoading as seoIsLoading,
  addMessage as addSEOMessage,
  updateProgressStep as updateSEOProgressStep,
  updateGapProgressStep as updateSEOGapProgressStep,
  handleRequestError as handleSEORequestError,
  initializeChatProgressSteps,
  initializeGapProgressSteps
} from '$lib/stores/seo-agent-store'

import {
  seoConversationStore,
  seoMessages as aguiSeoMessages,
  seoProgressSteps as aguiSeoProgressSteps,
  seoNicheKeywords as aguiSeoNicheKeywords,
  seoGapKeywords as aguiSeoGapKeywords,
  seoIsLoading as aguiSeoIsLoading,
  type SEOConversationState
} from '$lib/agui/seo-conversation-store'

import {
  seoCanvasStore,
  addSEOAnalysis,
  addKeywordResearch,
  addGapAnalysis,
  addContentStrategy,
  addNote,
  addToolResult,
  type SEOCanvasState,
  type SEOCanvasItem
} from '$lib/agui/seo-canvas-store'

import { EventType } from '$lib/agui/event-types'
import type { AGUIEventUnion } from '$lib/agui/event-types'

// Bridge configuration
interface BridgeConfig {
  enableTwoWaySync: boolean
  preserveLegacyBehavior: boolean
  enableFallbackHandling: boolean
  timeoutStrategy: 'progressive' | 'fixed'
  debugMode: boolean
}

const defaultBridgeConfig: BridgeConfig = {
  enableTwoWaySync: true,
  preserveLegacyBehavior: true,
  enableFallbackHandling: true,
  timeoutStrategy: 'progressive',
  debugMode: true
}

// SEO AG-UI Bridge Class
export class SEOAGUIBridge {
  private config: BridgeConfig
  private isActive: boolean = false
  private eventListeners: Array<() => void> = []
  private fallbackTimeouts: Map<string, number> = new Map()
  private isSyncing: boolean = false // Guard to prevent feedback loops

  constructor(config: Partial<BridgeConfig> = {}) {
    this.config = { ...defaultBridgeConfig, ...config }
    this.log('SEO AG-UI Bridge initialized', this.config)
  }

  // Start the bridge - sets up two-way synchronization
  start(): void {
    if (this.isActive) {
      this.log('Bridge already active')
      return
    }

    this.log('Starting SEO AG-UI Bridge')
    this.isActive = true

    if (this.config.enableTwoWaySync) {
      this.setupTwoWaySync()
    }

    this.log('SEO AG-UI Bridge started successfully')
  }

  // Stop the bridge
  stop(): void {
    if (!this.isActive) {
      this.log('Bridge not active')
      return
    }

    this.log('Stopping SEO AG-UI Bridge')
    this.isActive = false

    // Clear all event listeners
    this.eventListeners.forEach(unsubscribe => unsubscribe())
    this.eventListeners = []

    // Clear any pending timeouts
    this.fallbackTimeouts.forEach(timeoutId => clearTimeout(timeoutId))
    this.fallbackTimeouts.clear()

    this.log('SEO AG-UI Bridge stopped')
  }

  // Set up two-way synchronization between stores
  private setupTwoWaySync(): void {
    this.log('Setting up two-way synchronization')

    // Sync AG-UI messages to legacy store
    const unsubscribeMessages = aguiSeoMessages.subscribe(messages => {
      if (!this.isActive) return
      
      this.log('Syncing AG-UI messages to legacy store', messages.length)
      this.syncMessagesToLegacy(messages)
    })
    this.eventListeners.push(unsubscribeMessages)

    // Sync AG-UI progress to legacy store
    const unsubscribeProgress = aguiSeoProgressSteps.subscribe(steps => {
      if (!this.isActive) return
      
      this.log('Syncing AG-UI progress to legacy store', steps.length)
      this.syncProgressToLegacy(steps)
    })
    this.eventListeners.push(unsubscribeProgress)

    // Sync AG-UI keywords to legacy store
    const unsubscribeKeywords = aguiSeoNicheKeywords.subscribe(keywords => {
      if (!this.isActive) return
      
      this.log('Syncing AG-UI keywords to legacy store', keywords.length)
      this.syncKeywordsToLegacy(keywords)
    })
    this.eventListeners.push(unsubscribeKeywords)

    // Sync AG-UI gap keywords to legacy store
    const unsubscribeGapKeywords = aguiSeoGapKeywords.subscribe(gaps => {
      if (!this.isActive) return
      
      this.log('Syncing AG-UI gap keywords to legacy store', gaps.length)
      this.syncGapKeywordsToLegacy(gaps)
    })
    this.eventListeners.push(unsubscribeGapKeywords)

    // Sync AG-UI loading state to legacy store
    const unsubscribeLoading = aguiSeoIsLoading.subscribe(loading => {
      if (!this.isActive) return
      
      this.log('Syncing AG-UI loading state to legacy store', loading)
      seoIsLoading.set(loading)
    })
    this.eventListeners.push(unsubscribeLoading)

    this.log('Two-way synchronization setup complete')
  }

  // Sync AG-UI messages to legacy SEO store
  private syncMessagesToLegacy(aguiMessages: SEOConversationState['messages']): void {
    if (this.isSyncing) return // Prevent feedback loops
    
    this.isSyncing = true
    try {
      const currentLegacyMessages = get(seoMessages)
      
      console.log('=== SEO BRIDGE MESSAGE SYNC DEBUG ===')
      console.log('AG-UI messages count:', aguiMessages.length)
      console.log('Current legacy messages count:', currentLegacyMessages.length)
      console.log('AG-UI messages:', aguiMessages.map(m => ({ id: m.id, role: m.role, contentLength: m.content?.length || 0 })))
      
      // Find new messages that aren't in legacy store
      const newMessages = aguiMessages.filter(aguiMsg => 
        !currentLegacyMessages.some(legacyMsg => legacyMsg.id === aguiMsg.id)
      )

      console.log('New messages to sync:', newMessages.length)
      console.log('New messages details:', newMessages.map(m => ({ id: m.id, role: m.role, contentPreview: m.content?.substring(0, 100) })))

      // Add new messages to legacy store
      newMessages.forEach(message => {
        const seoMessage: SEOMessage = {
          id: message.id,
          role: message.role,
          content: message.content,
          timestamp: message.timestamp,
          isReport: message.role === 'assistant',
          data: undefined
        }
        
        console.log('Adding message to legacy store:', { id: seoMessage.id, role: seoMessage.role, contentLength: seoMessage.content?.length })
        addSEOMessage(seoMessage)
      })
      console.log('=== END BRIDGE MESSAGE SYNC DEBUG ===')
    } finally {
      this.isSyncing = false
    }
  }

  // Sync AG-UI progress to legacy SEO store
  private syncProgressToLegacy(aguiSteps: ProgressStep[]): void {
    if (this.isSyncing) return
    
    this.isSyncing = true
    try {
      aguiSteps.forEach(step => {
        updateSEOProgressStep(step.id, step.status, step.description)
      })
    } finally {
      this.isSyncing = false
    }
  }

  // Sync AG-UI keywords to legacy SEO store
  private syncKeywordsToLegacy(aguiKeywords: NicheKeyword[]): void {
    if (this.isSyncing) return
    
    this.isSyncing = true
    try {
      seoNicheKeywords.set(aguiKeywords)
    } finally {
      this.isSyncing = false
    }
  }

  // Sync AG-UI gap keywords to legacy SEO store
  private syncGapKeywordsToLegacy(aguiGaps: GapKeyword[]): void {
    if (this.isSyncing) return
    
    this.isSyncing = true
    try {
      seoGapKeywords.set(aguiGaps)
    } finally {
      this.isSyncing = false
    }
  }

  // Convert AG-UI events to legacy SEO progress steps
  convertAGUIEventToProgress(event: AGUIEventUnion): void {
    if (!this.isActive) return

    this.log('Converting AG-UI event to progress', event.type)

    switch (event.type) {
      case EventType.RUN_STARTED:
        this.handleRunStarted()
        break

      case EventType.TOOL_CALL_START:
        this.handleToolCallStart(event.toolName)
        break

      case EventType.TOOL_CALL_END:
        this.handleToolCallEnd(event.toolName, event.result)
        break

      case EventType.TOOL_CALL_ERROR:
        this.handleToolCallError(event.error)
        break

      case EventType.RUN_FINISHED:
        this.handleRunFinished()
        break

      case EventType.ERROR:
        this.handleError(event.error)
        break
    }
  }

  // Handle run started
  private handleRunStarted(): void {
    this.log('Run started - initializing progress')
    
    // Initialize appropriate progress steps based on context
    const currentState = get(seoConversationStore)
    if (currentState.mode === 'gap') {
      initializeGapProgressSteps()
    } else {
      initializeChatProgressSteps()
    }
  }

  // Handle tool call start
  private handleToolCallStart(toolName: string): void {
    this.log('Tool call started', toolName)
    
    // Map tool names to progress steps
    const toolProgressMap: Record<string, number> = {
      'webSearch': 1,
      'dataForSeoSearch': 2,
      'dataForSeoKeywordData': 3,
      'dataForSeoCompetitorAnalysis': 4,
      'dataForSeoGapAnalysis': 2,
      'default': 2
    }
    
    const stepId = toolProgressMap[toolName] || toolProgressMap.default
    
    const currentState = get(seoConversationStore)
    if (currentState.mode === 'gap') {
      updateSEOGapProgressStep(stepId, 'active')
    } else {
      updateSEOProgressStep(stepId, 'active')
    }
  }

  // Handle tool call end
  private handleToolCallEnd(toolName: string, result: any): void {
    this.log('Tool call ended', toolName, result)
    
    // Process tool results for SEO-specific data
    this.processSEOToolResult(toolName, result)
    
    // Update progress
    const toolProgressMap: Record<string, number> = {
      'webSearch': 1,
      'dataForSeoSearch': 2,
      'dataForSeoKeywordData': 3,
      'dataForSeoCompetitorAnalysis': 4,
      'dataForSeoGapAnalysis': 2,
      'default': 2
    }
    
    const stepId = toolProgressMap[toolName] || toolProgressMap.default
    
    const currentState = get(seoConversationStore)
    if (currentState.mode === 'gap') {
      updateSEOGapProgressStep(stepId, 'completed')
    } else {
      updateSEOProgressStep(stepId, 'completed')
    }
  }

  // Handle tool call error
  private handleToolCallError(error: string): void {
    this.log('Tool call error', error)
    
    if (this.config.enableFallbackHandling) {
      this.triggerFallbackStrategy(error)
    }
  }

  // Handle run finished
  private handleRunFinished(): void {
    this.log('Run finished')
    
    // Clear any pending fallback timeouts
    this.clearAllFallbackTimeouts()
    
    // Clear any timeout messages if they exist
    seoConversationStore.update(state => ({
      ...state,
      isLoading: false,
      hasPartialResults: false,
      isUsingMockData: false,
      // Remove any timeout/fallback messages
      messages: state.messages.filter(msg => 
        !msg.content.includes('Analysis taking longer than expected') &&
        !msg.content.includes('Extended analysis timeout')
      )
    }))
    
    // Complete all pending progress steps
    const currentState = get(seoConversationStore)
    const maxStep = Math.max(...currentState.progressSteps.map(s => s.id))
    
    if (currentState.mode === 'gap') {
      updateSEOGapProgressStep(maxStep, 'completed')
    } else {
      updateSEOProgressStep(maxStep, 'completed')
    }
  }

  // Handle general error
  private handleError(error: string): void {
    this.log('AG-UI error', error)
    
    if (this.config.enableFallbackHandling) {
      handleSEORequestError(new Error(error), 'server')
    }
  }

  // Process SEO tool results and route to appropriate stores/canvas
  private processSEOToolResult(toolName: string, result: any): void {
    try {
      this.log('Processing SEO tool result', toolName)
      
      // Route to canvas store for display
      addToolResult(toolName, result)
      
      // Extract and process specific SEO data types
      if (toolName.includes('dataForSeo') && result?.tasks?.[0]?.result) {
        const keywords = this.extractKeywordsFromDataForSEO(result)
        if (keywords.length > 0) {
          addKeywordResearch(keywords, `${toolName} query`)
          seoNicheKeywords.update(current => [...current, ...keywords])
        }
      }
      
      if (toolName.includes('gap') && result?.gaps) {
        const gaps = this.extractGapKeywords(result)
        if (gaps.length > 0) {
          addGapAnalysis(gaps, result.competitors || [])
          seoGapKeywords.update(current => [...current, ...gaps])
        }
      }
      
      if (toolName.includes('content') && result?.recommendations) {
        addContentStrategy(result.recommendations)
      }
      
    } catch (error) {
      this.log('Error processing SEO tool result', error)
    }
  }

  // Extract keywords from DataForSEO results
  private extractKeywordsFromDataForSEO(result: any): NicheKeyword[] {
    try {
      const keywords: NicheKeyword[] = []
      const items = result.tasks[0].result || []
      
      for (const item of items) {
        if (item.keyword && item.search_volume !== undefined) {
          keywords.push({
            keyword: item.keyword,
            search_volume: item.search_volume || 0,
            difficulty: item.keyword_difficulty || 0,
            competition: item.competition || 'unknown',
            cpc: item.cpc || 0,
            opportunity_score: this.calculateOpportunityScore(item)
          })
        }
      }
      
      return keywords
    } catch (error) {
      this.log('Error extracting keywords from DataForSEO', error)
      return []
    }
  }

  // Extract gap keywords from results
  private extractGapKeywords(result: any): GapKeyword[] {
    try {
      if (!result.gaps || !Array.isArray(result.gaps)) {
        return []
      }
      
      return result.gaps.map((gap: any) => ({
        keyword: gap.keyword || '',
        search_volume: gap.search_volume || 0,
        difficulty: gap.difficulty || 0,
        competition: gap.competition || 'unknown',
        cpc: gap.cpc || 0,
        competitor_position: gap.competitor_position || 0,
        your_position: gap.your_position || null,
        gap_type: gap.gap_type || 'opportunity',
        opportunity_score: this.calculateOpportunityScore(gap)
      }))
    } catch (error) {
      this.log('Error extracting gap keywords', error)
      return []
    }
  }

  // Calculate opportunity score
  private calculateOpportunityScore(item: any): number {
    const volume = item.search_volume || 0
    const difficulty = item.keyword_difficulty || item.difficulty || 100
    const cpc = item.cpc || 0
    
    if (volume === 0) return 0
    
    // Enhanced scoring formula
    let score = (volume / Math.max(difficulty, 1)) * 10
    
    // CPC bonus
    if (cpc > 0) {
      score *= (1 + Math.log(cpc + 1) / 10)
    }
    
    // Competition adjustment
    const competition = item.competition || 'unknown'
    if (competition === 'low') score *= 1.2
    else if (competition === 'medium') score *= 1.0
    else if (competition === 'high') score *= 0.8
    
    return Math.round(Math.max(score, 0))
  }

  // Trigger fallback strategy for timeouts and errors
  private triggerFallbackStrategy(error: string): void {
    this.log('Triggering fallback strategy', error)
    
    const fallbackId = `fallback_${Date.now()}`
    
    if (this.config.timeoutStrategy === 'progressive') {
      // Progressive timeout: try 30s, then 60s
      const timeoutId = window.setTimeout(() => {
        this.executeFallback('timeout', error)
      }, 30000)
      
      this.fallbackTimeouts.set(fallbackId, timeoutId)
      
      // Secondary fallback at 60s
      const secondaryTimeoutId = window.setTimeout(() => {
        this.executeFallback('extended_timeout', error)
      }, 60000)
      
      this.fallbackTimeouts.set(`${fallbackId}_secondary`, secondaryTimeoutId)
    } else {
      // Fixed timeout
      const timeoutId = window.setTimeout(() => {
        this.executeFallback('timeout', error)
      }, 30000)
      
      this.fallbackTimeouts.set(fallbackId, timeoutId)
    }
  }

  // Execute fallback handling
  private executeFallback(type: 'timeout' | 'extended_timeout', error: string): void {
    this.log('Executing fallback', type, error)
    
    // Update stores to show partial results
    seoConversationStore.update(state => ({
      ...state,
      isLoading: false,
      hasPartialResults: true,
      isUsingMockData: true
    }))
    
    // Add fallback message
    const fallbackMessage = type === 'extended_timeout' 
      ? 'Extended analysis timeout. Showing available partial results.'
      : 'Analysis taking longer than expected. Showing partial results.'
    
    addSEOMessage({
      role: 'assistant',
      content: fallbackMessage,
      isReport: true
    })
    
    // Handle error in legacy system
    handleSEORequestError(new Error(error), 'timeout')
  }

  // Clear all fallback timeouts
  private clearAllFallbackTimeouts(): void {
    this.fallbackTimeouts.forEach(timeoutId => clearTimeout(timeoutId))
    this.fallbackTimeouts.clear()
  }

  // Logging utility
  private log(message: string, ...data: any[]): void {
    if (this.config.debugMode) {
      console.log(`[SEO AG-UI Bridge] ${message}`, ...data)
    }
  }

  // Get bridge status
  getStatus(): { active: boolean; config: BridgeConfig; activeTimeouts: number } {
    return {
      active: this.isActive,
      config: this.config,
      activeTimeouts: this.fallbackTimeouts.size
    }
  }

  // Update bridge configuration
  updateConfig(newConfig: Partial<BridgeConfig>): void {
    this.config = { ...this.config, ...newConfig }
    this.log('Bridge configuration updated', this.config)
  }
}

// Create default bridge instance
export const seoAguiBridge = new SEOAGUIBridge()

// Export convenience functions
export const startSEOBridge = (config?: Partial<BridgeConfig>) => {
  if (config) {
    seoAguiBridge.updateConfig(config)
  }
  seoAguiBridge.start()
}

export const stopSEOBridge = () => {
  seoAguiBridge.stop()
}

export const getSEOBridgeStatus = () => {
  return seoAguiBridge.getStatus()
}

// Bridge auto-start is handled by the SEO agent page component to prevent unwanted subscriptions
