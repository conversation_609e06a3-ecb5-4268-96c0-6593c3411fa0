import { marked } from 'marked'

// Configure marked for security and proper rendering
marked.setOptions({
  gfm: true,
  breaks: true,
  headerIds: true,
  mangle: false
})

/**
 * Convert markdown content to HTML
 */
export function markdownToHtml(markdown: string): string {
  if (!markdown || typeof markdown !== 'string') {
    return ''
  }
  
  try {
    return marked(markdown) as string
  } catch (error) {
    console.error('Error converting markdown to HTML:', error)
    return markdown
  }
}

/**
 * Count words in text (removes markdown syntax for accurate count)
 */
export function countWords(text: string): number {
  if (!text || typeof text !== 'string') {
    return 0
  }
  
  // Remove markdown syntax for accurate word count
  const plainText = text
    .replace(/[#*_~`[\]()]/g, '') // Remove markdown characters
    .replace(/!\[.*?\]\(.*?\)/g, '') // Remove images
    .replace(/\[.*?\]\(.*?\)/g, '') // Remove links
    .replace(/```[\s\S]*?```/g, '') // Remove code blocks
    .replace(/`.*?`/g, '') // Remove inline code
    .replace(/>\s.*/g, '') // Remove blockquotes
    .replace(/^\s*[-*+]\s/gm, '') // Remove list markers
    .replace(/^\s*\d+\.\s/gm, '') // Remove numbered list markers
    .trim()
  
  if (!plainText) return 0
  
  return plainText.split(/\s+/).length
}

/**
 * Estimate reading time in minutes
 */
export function estimateReadingTime(text: string, wordsPerMinute: number = 200): number {
  const wordCount = countWords(text)
  return Math.ceil(wordCount / wordsPerMinute)
}

/**
 * Extract sections from markdown content based on headers
 */
export function extractSections(markdown: string): Array<{
  title: string
  content: string
  level: number
}> {
  if (!markdown) return []
  
  const lines = markdown.split('\n')
  const sections: Array<{ title: string, content: string, level: number }> = []
  let currentSection: { title: string, content: string[], level: number } | null = null
  
  for (const line of lines) {
    const headerMatch = line.match(/^(#{1,6})\s+(.+)$/)
    
    if (headerMatch) {
      // Save previous section if exists
      if (currentSection) {
        sections.push({
          title: currentSection.title,
          content: currentSection.content.join('\n').trim(),
          level: currentSection.level
        })
      }
      
      // Start new section
      currentSection = {
        title: headerMatch[2].trim(),
        content: [],
        level: headerMatch[1].length
      }
    } else if (currentSection) {
      currentSection.content.push(line)
    }
  }
  
  // Add the last section
  if (currentSection) {
    sections.push({
      title: currentSection.title,
      content: currentSection.content.join('\n').trim(),
      level: currentSection.level
    })
  }
  
  return sections
}

/**
 * Create a table of contents from markdown content
 */
export function createTableOfContents(markdown: string): Array<{
  title: string
  level: number
  anchor: string
}> {
  if (!markdown) return []
  
  const lines = markdown.split('\n')
  const toc: Array<{ title: string, level: number, anchor: string }> = []
  
  for (const line of lines) {
    const headerMatch = line.match(/^(#{1,6})\s+(.+)$/)
    
    if (headerMatch) {
      const title = headerMatch[2].trim()
      const level = headerMatch[1].length
      const anchor = title
        .toLowerCase()
        .replace(/[^\w\s-]/g, '')
        .replace(/\s+/g, '-')
        .trim()
      
      toc.push({ title, level, anchor })
    }
  }
  
  return toc
}

/**
 * Sanitize markdown content for safe display
 */
export function sanitizeMarkdown(markdown: string): string {
  if (!markdown) return ''
  
  // Basic sanitization - remove script tags and javascript: links
  return markdown
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/javascript:/gi, '')
    .replace(/on\w+\s*=/gi, '')
}