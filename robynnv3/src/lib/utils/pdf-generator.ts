import jsPD<PERSON> from 'jspdf'

export interface PDFExportOptions {
  filename?: string
  title?: string
  subtitle?: string
  author?: string
  creator?: string
}

export class PDFGenerator {
  private pdf: jsPDF
  private currentY: number = 20

  constructor() {
    this.pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    })
  }

  // Add header to the PDF
  private addHeader(title: string, subtitle?: string) {
    const pageWidth = this.pdf.internal.pageSize.getWidth()
    
    // Main title
    this.pdf.setFontSize(20)
    this.pdf.setFont('helvetica', 'bold')
    this.pdf.text(title, pageWidth / 2, 15, { align: 'center' })
    
    // Subtitle
    if (subtitle) {
      this.pdf.setFontSize(12)
      this.pdf.setFont('helvetica', 'normal')
      this.pdf.text(subtitle, pageWidth / 2, 25, { align: 'center' })
      this.currentY = 35
    } else {
      this.currentY = 25
    }
    
    // Horizontal line
    this.pdf.setLineWidth(0.5)
    this.pdf.line(20, this.currentY, pageWidth - 20, this.currentY)
    this.currentY += 10
  }

  // Add footer to the PDF
  private addFooter() {
    const pageWidth = this.pdf.internal.pageSize.getWidth()
    const pageHeight = this.pdf.internal.pageSize.getHeight()
    
    // Footer line
    this.pdf.setLineWidth(0.3)
    this.pdf.line(20, pageHeight - 20, pageWidth - 20, pageHeight - 20)
    
    // Footer text
    this.pdf.setFontSize(8)
    this.pdf.setFont('helvetica', 'normal')
    this.pdf.text('Generated by Robynn.ai SEO Agent', 20, pageHeight - 12)
    
    // Date
    const date = new Date().toLocaleDateString()
    this.pdf.text(`Generated on ${date}`, pageWidth - 20, pageHeight - 12, { align: 'right' })
    
    // Page number
    const pageNumber = this.pdf.internal.getCurrentPageInfo().pageNumber
    this.pdf.text(`Page ${pageNumber}`, pageWidth / 2, pageHeight - 12, { align: 'center' })
  }

  // Convert HTML element to PDF using jsPDF's native html() method
  async generateFromHTML(element: HTMLElement, options: PDFExportOptions = {}): Promise<Blob> {
    const {
      filename = `seo-analysis-${new Date().toISOString().slice(0, 10)}.pdf`,
      title = 'SEO Analysis Report',
      subtitle = `Generated on ${new Date().toLocaleDateString()}`,
      author = 'Robynn.ai',
      creator = 'Robynn.ai SEO Agent'
    } = options

    // Set PDF metadata
    this.pdf.setProperties({
      title,
      subject: 'SEO Analysis Report',
      author,
      creator,
      keywords: 'SEO, Analysis, Keywords, Strategy'
    })

    try {
      // Create a clone of the element to avoid modifying the original
      const clonedElement = element.cloneNode(true) as HTMLElement
      
      // Apply minimal, print-friendly styles to the clone
      this.applyPrintFriendlyStyles(clonedElement)
      
      // Temporarily add clone to DOM for rendering
      clonedElement.style.position = 'fixed'
      clonedElement.style.left = '-9999px'
      clonedElement.style.top = '0'
      clonedElement.style.width = '210mm' // A4 width
      clonedElement.style.zIndex = '-1000'
      document.body.appendChild(clonedElement)
      
      // Wait for rendering
      await new Promise(resolve => setTimeout(resolve, 100))

      console.log('=== USING JSPDF NATIVE HTML METHOD ===')
      console.log('Element to convert:', clonedElement)
      console.log('Element dimensions:', {
        width: clonedElement.offsetWidth,
        height: clonedElement.offsetHeight,
        scrollWidth: clonedElement.scrollWidth,
        scrollHeight: clonedElement.scrollHeight
      })

      // Use jsPDF's native html() method for superior quality
      return new Promise<Blob>((resolve, reject) => {
        this.pdf.html(clonedElement, {
          // Margins and positioning
          x: 20,
          y: this.currentY,
          width: 170, // A4 width minus margins (210 - 40)
          windowWidth: clonedElement.scrollWidth || 794,
          
          // High-quality html2canvas options
          html2canvas: {
            scale: window.devicePixelRatio || 2, // High DPI for crisp text
            useCORS: true,
            backgroundColor: '#ffffff',
            logging: false, // Reduce console noise
            letterRendering: true, // Better text rendering
            allowTaint: false,
            foreignObjectRendering: false,
            imageTimeout: 15000,
            removeContainer: true,
            onclone: (clonedDoc) => {
              this.ensureStylesInClone(clonedDoc);   // removes Tailwind / DaisyUI styles
              this.fixClonedDocumentColors(clonedDoc); // replaces any residual oklch()/var()
            }
          },
          
          // PDF generation callback
          callback: (doc) => {
            try {
              // Clean up the cloned element
              if (document.body.contains(clonedElement)) {
                document.body.removeChild(clonedElement)
              }

              // Add header to all pages
              const totalPages = doc.internal.pages.length - 1
              for (let i = 1; i <= totalPages; i++) {
                doc.setPage(i)
                
                // Add header to each page
                this.addHeaderToPage(doc, title, subtitle, i)
                
                // Add footer to each page
                this.addFooterToPage(doc, i, totalPages)
              }

              console.log(`PDF generated successfully with ${totalPages} pages`)
              
              // Return as blob
              const pdfBlob = doc.output('blob')
              resolve(pdfBlob)
              
            } catch (callbackError) {
              console.error('Error in PDF callback:', callbackError)
              reject(callbackError)
            }
          },
          
          // Error handling
          autoPaging: 'text', // Better page breaking for text content
          margin: [15, 20, 15, 20] // top, right, bottom, left margins
        })
      })

    } catch (error) {
      console.error('Error generating PDF:', error)
      throw new Error('Failed to generate PDF. Please try again.')
    }
  }

  // Add header to a specific page
  private addHeaderToPage(doc: jsPDF, title: string, subtitle?: string, pageNumber?: number) {
    const pageWidth = doc.internal.pageSize.getWidth()
    
    // Main title
    doc.setFontSize(20)
    doc.setFont('helvetica', 'bold')
    doc.setTextColor(0, 0, 0) // Black text
    doc.text(title, pageWidth / 2, 15, { align: 'center' })
    
    // Subtitle
    if (subtitle) {
      doc.setFontSize(12)
      doc.setFont('helvetica', 'normal')
      doc.text(subtitle, pageWidth / 2, 25, { align: 'center' })
    }
    
    // Horizontal line
    doc.setLineWidth(0.5)
    doc.setDrawColor(0, 0, 0) // Black line
    doc.line(20, 30, pageWidth - 20, 30)
  }

  // Add footer to a specific page
  private addFooterToPage(doc: jsPDF, pageNumber: number, totalPages: number) {
    const pageWidth = doc.internal.pageSize.getWidth()
    const pageHeight = doc.internal.pageSize.getHeight()
    
    // Footer line
    doc.setLineWidth(0.3)
    doc.setDrawColor(200, 200, 200) // Light gray line
    doc.line(20, pageHeight - 20, pageWidth - 20, pageHeight - 20)
    
    // Footer text
    doc.setFontSize(8)
    doc.setFont('helvetica', 'normal')
    doc.setTextColor(100, 100, 100) // Gray text
    doc.text('Generated by Robynn.ai SEO Agent', 20, pageHeight - 12)
    
    // Date
    const date = new Date().toLocaleDateString()
    doc.text(`Generated on ${date}`, pageWidth - 20, pageHeight - 12, { align: 'right' })
    
    // Page number
    doc.text(`Page ${pageNumber} of ${totalPages}`, pageWidth / 2, pageHeight - 12, { align: 'center' })
  }

  // Apply minimal, print-friendly styles (simplified approach)
  private applyPrintFriendlyStyles(element: HTMLElement) {
    // Apply basic print styles without aggressive manipulation
    element.style.fontFamily = 'Arial, Helvetica, sans-serif'
    element.style.fontSize = '14px'
    element.style.lineHeight = '1.6'
    element.style.color = '#333333'
    element.style.backgroundColor = '#ffffff'
    element.style.padding = '20px'
    element.style.boxSizing = 'border-box'
    element.style.maxWidth = 'none'
    element.style.overflow = 'visible'

    // Clean up problematic CSS selectively
    this.cleanupModernCSS(element)
    
    // Style tables for better PDF rendering
    const tables = element.querySelectorAll('table')
    tables.forEach(table => {
      const htmlTable = table as HTMLElement
      htmlTable.style.borderCollapse = 'collapse'
      htmlTable.style.width = '100%'
      htmlTable.style.marginBottom = '20px'
      htmlTable.style.backgroundColor = '#ffffff'
      
      // Style table headers and cells
      const headers = table.querySelectorAll('th')
      headers.forEach(header => {
        const htmlHeader = header as HTMLElement
        htmlHeader.style.backgroundColor = '#f8f9fa'
        htmlHeader.style.border = '1px solid #dee2e6'
        htmlHeader.style.padding = '12px 8px'
        htmlHeader.style.fontWeight = 'bold'
        htmlHeader.style.color = '#333333'
      })
      
      const cells = table.querySelectorAll('td')
      cells.forEach(cell => {
        const htmlCell = cell as HTMLElement
        htmlCell.style.border = '1px solid #dee2e6'
        htmlCell.style.padding = '8px'
        htmlCell.style.color = '#333333'
        htmlCell.style.backgroundColor = '#ffffff'
      })
    })

    // Style message containers
    const messageContainers = element.querySelectorAll('.bg-primary, .bg-base-200, .bg-base-100')
    messageContainers.forEach(container => {
      const htmlContainer = container as HTMLElement
      htmlContainer.style.border = '1px solid #dee2e6'
      htmlContainer.style.borderRadius = '8px'
      htmlContainer.style.padding = '16px'
      htmlContainer.style.marginBottom = '16px'
      
      // Determine background based on class
      if (container.classList.contains('bg-primary')) {
        htmlContainer.style.backgroundColor = '#0066cc'
        htmlContainer.style.color = '#ffffff'
      } else {
        htmlContainer.style.backgroundColor = '#f8f9fa'
        htmlContainer.style.color = '#333333'
      }
    })

    // Style headings
    const headings = element.querySelectorAll('h1, h2, h3, h4, h5, h6')
    headings.forEach(heading => {
      const htmlHeading = heading as HTMLElement
      htmlHeading.style.color = '#2c3e50'
      htmlHeading.style.marginBottom = '12px'
      htmlHeading.style.marginTop = '20px'
      htmlHeading.style.fontWeight = 'bold'
    })
  }

  // Clean up modern CSS that causes issues
  private cleanupModernCSS(element: HTMLElement) {
    const allElements = [element, ...Array.from(element.querySelectorAll('*'))]
    
    allElements.forEach(el => {
      const htmlEl = el as HTMLElement
      if (!htmlEl.style) return
      
      // Remove problematic CSS functions
      const styleProps = ['backgroundColor', 'color', 'borderColor']
      styleProps.forEach(prop => {
        const value = htmlEl.style[prop as any]
        if (value && (value.includes('oklch') || value.includes('var(') || value.includes('color('))) {
          // Replace with safe fallbacks
          if (prop === 'backgroundColor') {
            htmlEl.style.backgroundColor = '#ffffff'
          } else if (prop === 'color') {
            htmlEl.style.color = '#333333'
          } else if (prop === 'borderColor') {
            htmlEl.style.borderColor = '#dee2e6'
          }
        }
      })
    })
  }

  // Legacy method kept for backward compatibility
  private applyPrintStyles(element: HTMLElement) {
    // Ensure proper styling for PDF export
    element.style.fontFamily = 'Arial, sans-serif'
    element.style.fontSize = '12px'
    element.style.lineHeight = '1.4'
    element.style.color = '#000000'
    element.style.backgroundColor = '#ffffff'
    element.style.width = '794px' // Use pixels for better compatibility
    element.style.minHeight = 'auto'
    element.style.padding = '20px'
    element.style.boxSizing = 'border-box'
    
    // Aggressively remove all problematic CSS before processing
    this.stripProblematicCSS(element)
    
    // Fix color issues by converting modern CSS to compatible formats
    this.convertModernColors(element)
    
    // Style tables for better PDF rendering
    const tables = element.querySelectorAll('table')
    tables.forEach(table => {
      const htmlTable = table as HTMLElement
      htmlTable.style.borderCollapse = 'collapse'
      htmlTable.style.width = '100%'
      htmlTable.style.marginBottom = '20px'
      
      // Style table headers
      const headers = table.querySelectorAll('th')
      headers.forEach(header => {
        const htmlHeader = header as HTMLElement
        htmlHeader.style.backgroundColor = '#f5f5f5'
        htmlHeader.style.border = '1px solid #ddd'
        htmlHeader.style.padding = '8px'
        htmlHeader.style.fontWeight = 'bold'
      })
      
      // Style table cells
      const cells = table.querySelectorAll('td')
      cells.forEach(cell => {
        const htmlCell = cell as HTMLElement
        htmlCell.style.border = '1px solid #ddd'
        htmlCell.style.padding = '8px'
      })
    })

    // Style content cards
    const cards = element.querySelectorAll('.bg-base-100, .bg-base-200')
    cards.forEach(card => {
      const htmlCard = card as HTMLElement
      htmlCard.style.border = '1px solid #ddd'
      htmlCard.style.borderRadius = '4px'
      htmlCard.style.padding = '16px'
      htmlCard.style.marginBottom = '16px'
      htmlCard.style.backgroundColor = '#f9f9f9'
    })

    // Style headings
    const headings = element.querySelectorAll('h1, h2, h3, h4, h5, h6')
    headings.forEach(heading => {
      const htmlHeading = heading as HTMLElement
      htmlHeading.style.color = '#333333'
      htmlHeading.style.marginBottom = '8px'
      htmlHeading.style.fontWeight = 'bold'
    })
  }

  // Aggressively strip problematic CSS that html2canvas can't handle
  private stripProblematicCSS(element: HTMLElement) {
    const allElements = [element, ...Array.from(element.querySelectorAll('*'))]
    
    allElements.forEach(el => {
      const htmlEl = el as HTMLElement
      if (!htmlEl.style) return
      
      try {
        // Remove any CSS custom properties that might contain modern color functions
        const cssText = htmlEl.style.cssText
        if (cssText) {
          // Remove lines containing problematic functions
          const cleanCss = cssText
            .split(';')
            .filter(rule => {
              const cleanRule = rule.trim()
              return !cleanRule.includes('oklch(') && 
                     !cleanRule.includes('color(') &&
                     !cleanRule.includes('--') && // Remove CSS custom properties
                     !cleanRule.includes('var(') // Remove CSS variable references
            })
            .join(';')
          
          // Clear and re-apply only safe CSS
          htmlEl.style.cssText = ''
          try {
            htmlEl.style.cssText = cleanCss
          } catch (e) {
            // If parsing fails, just apply basic styles
            htmlEl.style.backgroundColor = '#ffffff'
            htmlEl.style.color = '#333333'
          }
        }
        
        // Force override any remaining problematic styles
        if (htmlEl.style.backgroundColor && (
          htmlEl.style.backgroundColor.includes('oklch') || 
          htmlEl.style.backgroundColor.includes('var(') ||
          htmlEl.style.backgroundColor.includes('color(')
        )) {
          htmlEl.style.backgroundColor = '#ffffff'
        }
        
        if (htmlEl.style.color && (
          htmlEl.style.color.includes('oklch') || 
          htmlEl.style.color.includes('var(') ||
          htmlEl.style.color.includes('color(')
        )) {
          htmlEl.style.color = '#333333'
        }
        
      } catch (e) {
        // If all else fails, apply basic safe styles
        try {
          htmlEl.style.backgroundColor = '#ffffff'
          htmlEl.style.color = '#333333'
        } catch (fallbackError) {
          // Ignore if even fallback fails
        }
      }
    })
  }

  // Convert modern CSS colors to compatible formats
  private convertModernColors(element: HTMLElement) {
    // Color mapping for common modern CSS functions to hex equivalents
    const colorMap: Record<string, string> = {
      'oklch(0.69 0.14 21)': '#d97706', // Primary orange
      'oklch(0.98 0.008 85)': '#ffffff', // White
      'oklch(0.27 0.015 261)': '#333333', // Dark text
      'oklch(0.52 0.015 261)': '#666666', // Muted text
      'oklch(0.95 0.013 261)': '#f5f5f5', // Light background
      'oklch(0.92 0.016 261)': '#e5e5e5', // Border color
    }
    
    // Get all elements including the root
    const allElements = [element, ...Array.from(element.querySelectorAll('*'))]
    
    allElements.forEach(el => {
      const htmlEl = el as HTMLElement
      if (!htmlEl.style) return
      
      // Convert background colors
      const bgColor = htmlEl.style.backgroundColor
      if (bgColor && (bgColor.includes('oklch') || bgColor.includes('hsl') || bgColor.includes('rgb'))) {
        htmlEl.style.backgroundColor = this.convertColorToHex(bgColor, colorMap)
      }
      
      // Convert text colors
      const textColor = htmlEl.style.color
      if (textColor && (textColor.includes('oklch') || textColor.includes('hsl') || textColor.includes('rgb'))) {
        htmlEl.style.color = this.convertColorToHex(textColor, colorMap)
      }
      
      // Convert border colors
      const borderColor = htmlEl.style.borderColor
      if (borderColor && (borderColor.includes('oklch') || borderColor.includes('hsl') || borderColor.includes('rgb'))) {
        htmlEl.style.borderColor = this.convertColorToHex(borderColor, colorMap)
      }
    })
  }
  
  // Helper to convert color values
  private convertColorToHex(color: string, colorMap: Record<string, string>): string {
    // Check direct mapping first
    for (const [modern, hex] of Object.entries(colorMap)) {
      if (color.includes(modern.replace(/oklch\((.*?)\)/, '$1'))) {
        return hex
      }
    }
    
    // Fallback mappings for common patterns
    if (color.includes('oklch')) {
      // Extract lightness to determine if it's light or dark
      const lightnessMatch = color.match(/oklch\(([\d.]+)/)
      if (lightnessMatch) {
        const lightness = parseFloat(lightnessMatch[1])
        if (lightness > 0.9) return '#ffffff' // Very light
        if (lightness > 0.7) return '#f5f5f5' // Light
        if (lightness > 0.5) return '#cccccc' // Medium
        if (lightness > 0.3) return '#666666' // Dark
        return '#333333' // Very dark
      }
    }
    
    // Return original if no conversion needed
    return color
  }

  // Fix colors in cloned document
  private fixClonedDocumentColors(clonedDoc: Document) {
    const allElements = clonedDoc.querySelectorAll('*')
    allElements.forEach(el => {
      const htmlEl = el as HTMLElement
      if (!htmlEl) return
      
      try {
        // Force safe colors for elements that might have modern CSS
        const computedStyle = clonedDoc.defaultView?.getComputedStyle(htmlEl)
        if (computedStyle) {
          // Check and fix background color
          const bgColor = computedStyle.backgroundColor
          if (bgColor && (bgColor.includes('oklch') || bgColor.includes('color(') || bgColor === 'rgba(0, 0, 0, 0)')) {
            htmlEl.style.setProperty('background-color', '#ffffff', 'important')
          }
          
          // Check and fix text color
          const textColor = computedStyle.color
          if (textColor && (textColor.includes('oklch') || textColor.includes('color('))) {
            htmlEl.style.setProperty('color', '#333333', 'important')
          }
          
          // Check and fix border color
          const borderColor = computedStyle.borderColor
          if (borderColor && (borderColor.includes('oklch') || borderColor.includes('color('))) {
            htmlEl.style.setProperty('border-color', '#cccccc', 'important')
          }
        }
        
        // Additional safety: Override any CSS custom properties that might contain problematic colors
        const cssText = htmlEl.style.cssText
        if (cssText && (cssText.includes('oklch') || cssText.includes('color('))) {
          // Force safe defaults
          htmlEl.style.setProperty('background-color', '#ffffff', 'important')
          htmlEl.style.setProperty('color', '#333333', 'important')
        }
      } catch (e) {
        // Ignore errors and apply safe fallbacks
        try {
          htmlEl.style.setProperty('background-color', '#ffffff', 'important')
          htmlEl.style.setProperty('color', '#333333', 'important')
        } catch (fallbackError) {
          // If even the fallback fails, just continue
          console.warn('Color fallback failed:', fallbackError)
        }
      }
    })
  }

  // Ensure styles are properly applied in cloned document
  private ensureStylesInClone(clonedDoc: Document) {
    // Aggressively remove ALL external stylesheets and problematic styles
    const allStyles = clonedDoc.querySelectorAll('style, link[rel="stylesheet"], link[href*="tailwind"], link[href*="daisyui"]')
    allStyles.forEach(styleEl => {
      // Remove all external stylesheets to prevent oklch issues
      styleEl.remove()
    })
    
    // Remove any remaining style attributes that contain modern CSS
    const allElements = clonedDoc.querySelectorAll('*')
    allElements.forEach(el => {
      const htmlEl = el as HTMLElement
      if (htmlEl.style && htmlEl.style.cssText) {
        const cssText = htmlEl.style.cssText
        if (cssText.includes('oklch') || cssText.includes('color(') || cssText.includes('var(')) {
          // Clear the problematic styles
          htmlEl.style.cssText = ''
        }
      }
    })
    
    // Copy important styles to the cloned document
    const style = clonedDoc.createElement('style')
    style.textContent = `
      * {
        box-sizing: border-box !important;
      }
      
      /* Container background */
      body {
        background-color: white !important;
      }
      
      /* Content container styling */
      .seo-export-content,
      .space-y-4 {
        background-color: white !important;
        color: #333333 !important;
        padding: 20px !important;
        width: 100% !important;
        max-width: none !important;
      }
      
      /* Message bubbles - preserve visibility */
      .bg-primary {
        background-color: #d97706 !important;
        color: white !important;
      }
      
      .bg-base-200 {
        background-color: #f5f5f5 !important;
        color: #333333 !important;
      }
      
      /* Text elements */
      div, span, p {
        color: inherit !important;
      }
      
      .table {
        border-collapse: collapse;
        width: 100%;
        margin-bottom: 20px;
      }
      
      .table th,
      .table td {
        border: 1px solid #ddd !important;
        padding: 8px !important;
        text-align: left !important;
        background-color: white !important;
        color: #333333 !important;
      }
      
      .table th {
        background-color: #f5f5f5 !important;
        font-weight: bold !important;
        color: #333333 !important;
      }
      
      .bg-primary,
      .bg-primary\\/ 10,
      [class*="bg-primary"] {
        background-color: #d97706 !important;
        color: white !important;
      }
      
      .text-primary,
      [class*="text-primary"] {
        color: #d97706 !important;
      }
      
      .border,
      [class*="border"] {
        border: 1px solid #ddd !important;
      }
      
      .rounded-lg,
      [class*="rounded"] {
        border-radius: 8px !important;
      }
      
      .p-4,
      [class*="p-"] {
        padding: 16px !important;
      }
      
      .mb-4,
      [class*="mb-"] {
        margin-bottom: 16px !important;
      }
      
      .text-foreground,
      [class*="text-foreground"] {
        color: #333333 !important;
      }
      
      .text-muted-foreground,
      [class*="text-muted-foreground"] {
        color: #666666 !important;
      }
      
      .bg-base-100,
      .bg-base-200,
      [class*="bg-base"] {
        background-color: #f9f9f9 !important;
        color: #333333 !important;
      }
      
      /* Headings */
      h1, h2, h3, h4, h5, h6 {
        color: #333333 !important;
        font-weight: bold !important;
      }
      
      /* Safe button styles */
      button, .btn {
        background-color: #f5f5f5 !important;
        color: #333333 !important;
        border: 1px solid #ddd !important;
      }
    `
    clonedDoc.head.appendChild(style)
  }

  // Download the PDF directly
  async downloadPDF(element: HTMLElement, options: PDFExportOptions = {}) {
    try {
      const blob = await this.generateFromHTML(element, options)
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = options.filename || `seo-analysis-${new Date().toISOString().slice(0, 10)}.pdf`
      link.click()
      URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Error downloading PDF:', error)
      throw error
    }
  }
}

// Export utility function for easy use
export async function exportToPDF(element: HTMLElement, options: PDFExportOptions = {}) {
  const generator = new PDFGenerator()
  return generator.downloadPDF(element, options)
}
