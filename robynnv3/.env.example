# Supabase (required)
PUBLIC_SUPABASE_URL=your_supabase_url
PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# AI/LLM Providers (choose one or more)
# OpenAI
OPENAI_API_KEY=sk-...

# Anthropic Claude
ANTHROPIC_API_KEY=sk-ant-...

# Google Gemini
GOOGLE_GENERATIVE_AI_API_KEY=...

# DeepSeek
DEEPSEEK_API_KEY=...

# LLM Configuration (optional - defaults to OpenAI gpt-4o-mini)
LLM_PROVIDER=openai
LLM_MODEL=gpt-4o-mini

# Email (optional)
RESEND_API_KEY=re_...

# SMTP Configuration (required for Supabase email confirmations)
# These are used by Supabase for sending confirmation emails
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password

# Stripe (optional)
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...

# Research APIs (for Orchestrator Agent)
EXA_API_KEY=your_exa_api_key_here
APOLLO_API_KEY=your_apollo_api_key_here

# Firecrawl API (for enhanced web scraping and data extraction)
FIRECRAWL_API_KEY=fc-your_firecrawl_api_key_here

# Slack Webhooks
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/your_webhook_url_here
SLACK_USER_SIGNUPS_WEBHOOK_URL=https://hooks.slack.com/services/your_user_signups_webhook_url_here
