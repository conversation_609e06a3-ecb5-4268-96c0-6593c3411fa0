-- Quick Database Check and Fix
-- Run this in your Supabase SQL Editor to check and fix the database

-- 1. Check if content_sections table exists
SELECT EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = 'content_sections'
) as table_exists;

-- 2. Check if markdown_content column exists in content_documents
SELECT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_schema = 'public' 
    AND table_name = 'content_documents' 
    AND column_name = 'markdown_content'
) as markdown_column_exists;

-- 3. If you see FALSE for either above, run the full migration below:
-- ========================================================================

-- PRODUCTION-SAFE MIGRATION: Add markdown content support
BEGIN;

-- Add new columns to content_documents table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'content_documents' 
        AND column_name = 'markdown_content'
    ) THEN
        ALTER TABLE "public"."content_documents" ADD COLUMN "markdown_content" TEXT;
        RAISE NOTICE 'Added markdown_content column';
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'content_documents' 
        AND column_name = 'content_mode'
    ) THEN
        ALTER TABLE "public"."content_documents" 
        ADD COLUMN "content_mode" TEXT NOT NULL DEFAULT 'single'
        CHECK ("content_mode" IN ('single', 'accumulation'));
        RAISE NOTICE 'Added content_mode column';
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'content_documents' 
        AND column_name = 'section_count'
    ) THEN
        ALTER TABLE "public"."content_documents" 
        ADD COLUMN "section_count" INTEGER NOT NULL DEFAULT 0;
        RAISE NOTICE 'Added section_count column';
    END IF;
END $$;

-- Create content_sections table
CREATE TABLE IF NOT EXISTS "public"."content_sections" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "document_id" UUID NOT NULL REFERENCES "public"."content_documents"("id") ON DELETE CASCADE,
    "user_id" UUID NOT NULL REFERENCES "auth"."users"("id") ON DELETE CASCADE,
    "section_title" TEXT,
    "section_content" TEXT NOT NULL,
    "agent_source" TEXT,
    "original_prompt" TEXT,
    "order_index" INTEGER NOT NULL DEFAULT 0,
    "metadata" JSONB DEFAULT '{}',
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT now(),
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT now(),
    PRIMARY KEY ("id")
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_content_sections_document_id ON "public"."content_sections"("document_id");
CREATE INDEX IF NOT EXISTS idx_content_sections_user_id ON "public"."content_sections"("user_id");
CREATE INDEX IF NOT EXISTS idx_content_sections_order ON "public"."content_sections"("document_id", "order_index");

-- Enable RLS
ALTER TABLE "public"."content_sections" ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can manage their own content sections" ON "public"."content_sections"
    FOR ALL USING (auth.uid() = user_id);

-- Create markdown regeneration function
CREATE OR REPLACE FUNCTION regenerate_markdown_content(doc_id UUID)
RETURNS TEXT AS $$
DECLARE
    result TEXT;
BEGIN
    SELECT string_agg(
        COALESCE('## ' || section_title, '') || 
        E'\n\n' || 
        COALESCE('*Prompt: ' || original_prompt || '*' || E'\n\n', '') ||
        section_content || 
        E'\n\n---\n',
        E'\n' ORDER BY order_index
    ) INTO result
    FROM content_sections
    WHERE document_id = doc_id;
    
    RETURN COALESCE(result, '');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT ALL ON "public"."content_sections" TO authenticated;
GRANT ALL ON "public"."content_sections" TO service_role;

COMMIT;

-- 4. Verify the fix worked
SELECT 
    'content_sections table exists: ' || 
    EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'content_sections')::text as status;