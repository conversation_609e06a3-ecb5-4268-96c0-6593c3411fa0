# Test Instructions for "Add to Canvas" Fix

## What was fixed
The "Add to Canvas" button was not appearing for assistant messages in the EnhancedAgentSidebar component. The issue was that:

1. The button was only shown in a confirmation dialog (which required `showConfirmation = true`)
2. The confirmation dialog was automatically shown after each response, but not for historical messages
3. There was no way to add previous messages to the canvas

## Changes made
1. Added an "Add to Canvas" button to each assistant message in the message list
2. Clicking the button sets up the confirmation dialog with that specific message's content
3. Removed automatic confirmation dialog popup after responses (users now manually choose)

## How to test

1. Start the development server:
   ```bash
   pnpm run dev
   ```

2. Navigate to the Content Board:
   - Go to http://localhost:5176 (or whatever port is shown)
   - Log in if needed
   - Navigate to Dashboard → Content Board

3. Create or open a document:
   - Click "New Document" or select an existing one
   - Make sure you're in the document editor view

4. Open the Agent Sidebar:
   - Click the "Open Agent" button in the top right of the editor

5. Test the fix:
   - Send a message to the agent (e.g., "Tell me about content marketing")
   - Wait for the response to complete
   - You should now see an "Add to Canvas" button below each assistant message
   - Click the "Add to Canvas" button
   - A confirmation dialog should appear
   - Click "Add to Canvas" in the dialog
   - The content should be added to your document

## Expected behavior
- Each assistant message should have its own "Add to Canvas" button
- Clicking the button should open a confirmation dialog
- Confirming should trigger the `handleConfirmAppend` function
- The content should be appended to the current document

## Files modified
- `/src/lib/components/EnhancedAgentSidebar.svelte`
  - Added "Add to Canvas" button to each assistant message (lines 483-499)
  - Removed automatic confirmation dialog popup (lines 236-240)