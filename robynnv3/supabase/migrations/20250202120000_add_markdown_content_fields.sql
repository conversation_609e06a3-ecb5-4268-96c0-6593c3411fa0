-- PRODUCTION-SAFE MIGRATION: Add markdown content support to content_documents and create content_sections table
-- This migration adds the ability to store content in markdown format and supports content accumulation
-- ✅ SAFE FOR PRODUCTION: Only adds new columns with defaults and new tables
-- ✅ IDEMPOTENT: Can be run multiple times safely
-- ✅ NON-BREAKING: Existing data and functionality remains intact

-- Begin transaction for safety
BEGIN;

-- Add new columns to content_documents table (safe - only adds columns with defaults)
DO $$
BEGIN
    -- Add markdown_content column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'content_documents' 
        AND column_name = 'markdown_content'
    ) THEN
        ALTER TABLE "public"."content_documents" ADD COLUMN "markdown_content" TEXT;
        RAISE NOTICE 'Added markdown_content column to content_documents';
    ELSE
        RAISE NOTICE 'markdown_content column already exists in content_documents';
    END IF;
    
    -- Add content_mode column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'content_documents' 
        AND column_name = 'content_mode'
    ) THEN
        ALTER TABLE "public"."content_documents" ADD COLUMN "content_mode" TEXT NOT NULL DEFAULT 'single';
        RAISE NOTICE 'Added content_mode column to content_documents';
    ELSE
        RAISE NOTICE 'content_mode column already exists in content_documents';
    END IF;
    
    -- Add section_count column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'content_documents' 
        AND column_name = 'section_count'
    ) THEN
        ALTER TABLE "public"."content_documents" ADD COLUMN "section_count" INTEGER NOT NULL DEFAULT 0;
        RAISE NOTICE 'Added section_count column to content_documents';
    ELSE
        RAISE NOTICE 'section_count column already exists in content_documents';
    END IF;
END $$;

-- Add check constraint for content_mode (safe - only adds constraint if not exists)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_schema = 'public' 
        AND table_name = 'content_documents' 
        AND constraint_name = 'content_documents_content_mode_check'
    ) THEN
        ALTER TABLE "public"."content_documents"
        ADD CONSTRAINT "content_documents_content_mode_check" CHECK (content_mode IN ('single', 'accumulation'));
        RAISE NOTICE 'Added content_mode check constraint to content_documents';
    ELSE
        RAISE NOTICE 'content_mode check constraint already exists in content_documents';
    END IF;
END $$;

-- Create content_sections table for storing individual appended content blocks (safe - only if not exists)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'content_sections'
    ) THEN
        CREATE TABLE "public"."content_sections" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid(),
            "document_id" UUID NOT NULL,
            "user_id" UUID NOT NULL,
            "section_title" TEXT NOT NULL,
            "section_content" TEXT NOT NULL,
            "agent_source" TEXT,
            "original_prompt" TEXT,
            "order_index" INTEGER NOT NULL,
            "metadata" JSONB NOT NULL DEFAULT '{}',
            "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
        );
        RAISE NOTICE 'Created content_sections table';
    ELSE
        RAISE NOTICE 'content_sections table already exists';
    END IF;
END $$;

-- Create primary key for content_sections (safe - only if not exists)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_schema = 'public' 
        AND table_name = 'content_sections' 
        AND constraint_name = 'content_sections_pkey'
    ) THEN
        CREATE UNIQUE INDEX IF NOT EXISTS content_sections_pkey ON public.content_sections USING btree (id);
        ALTER TABLE "public"."content_sections"
        ADD CONSTRAINT "content_sections_pkey" PRIMARY KEY USING INDEX "content_sections_pkey";
        RAISE NOTICE 'Added primary key to content_sections';
    ELSE
        RAISE NOTICE 'Primary key already exists for content_sections';
    END IF;
END $$;

-- Add foreign key constraints (safe - only if not exists)
DO $$
BEGIN
    -- Document ID foreign key
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_schema = 'public' 
        AND table_name = 'content_sections' 
        AND constraint_name = 'content_sections_document_id_fkey'
    ) THEN
        ALTER TABLE "public"."content_sections"
        ADD CONSTRAINT "content_sections_document_id_fkey" 
        FOREIGN KEY (document_id) REFERENCES content_documents(id) ON UPDATE CASCADE ON DELETE CASCADE;
        RAISE NOTICE 'Added document_id foreign key to content_sections';
    ELSE
        RAISE NOTICE 'document_id foreign key already exists for content_sections';
    END IF;

    -- User ID foreign key
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_schema = 'public' 
        AND table_name = 'content_sections' 
        AND constraint_name = 'content_sections_user_id_fkey'
    ) THEN
        ALTER TABLE "public"."content_sections"
        ADD CONSTRAINT "content_sections_user_id_fkey" 
        FOREIGN KEY (user_id) REFERENCES auth.users(id) ON UPDATE CASCADE ON DELETE CASCADE;
        RAISE NOTICE 'Added user_id foreign key to content_sections';
    ELSE
        RAISE NOTICE 'user_id foreign key already exists for content_sections';
    END IF;
END $$;

-- Create indexes for performance (safe - IF NOT EXISTS)
CREATE INDEX IF NOT EXISTS content_sections_document_id_idx ON public.content_sections USING btree (document_id);
CREATE INDEX IF NOT EXISTS content_sections_user_id_idx ON public.content_sections USING btree (user_id);
CREATE INDEX IF NOT EXISTS content_sections_order_idx ON public.content_sections USING btree (document_id, order_index);
CREATE INDEX IF NOT EXISTS content_sections_created_at_idx ON public.content_sections USING btree (created_at);

-- Enable Row Level Security (safe - can be run multiple times)
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'content_sections'
    ) THEN
        ALTER TABLE "public"."content_sections" ENABLE ROW LEVEL SECURITY;
        RAISE NOTICE 'Enabled RLS for content_sections';
    END IF;
END $$;

-- Grant permissions for content_sections (safe - can be run multiple times)
-- Permissions for authenticated users
GRANT DELETE ON TABLE "public"."content_sections" TO "authenticated";
GRANT INSERT ON TABLE "public"."content_sections" TO "authenticated";
GRANT REFERENCES ON TABLE "public"."content_sections" TO "authenticated";
GRANT SELECT ON TABLE "public"."content_sections" TO "authenticated";
GRANT TRIGGER ON TABLE "public"."content_sections" TO "authenticated";
GRANT TRUNCATE ON TABLE "public"."content_sections" TO "authenticated";
GRANT UPDATE ON TABLE "public"."content_sections" TO "authenticated";

-- Permissions for service role
GRANT DELETE ON TABLE "public"."content_sections" TO "service_role";
GRANT INSERT ON TABLE "public"."content_sections" TO "service_role";
GRANT REFERENCES ON TABLE "public"."content_sections" TO "service_role";
GRANT SELECT ON TABLE "public"."content_sections" TO "service_role";
GRANT TRIGGER ON TABLE "public"."content_sections" TO "service_role";
GRANT TRUNCATE ON TABLE "public"."content_sections" TO "service_role";
GRANT UPDATE ON TABLE "public"."content_sections" TO "service_role";

-- Row Level Security Policies for content_sections (safe - only if not exists)
DO $$
BEGIN
    -- Policy for viewing sections
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename = 'content_sections' 
        AND policyname = 'Users can view sections for their documents'
    ) THEN
        CREATE POLICY "Users can view sections for their documents" ON "public"."content_sections" AS PERMISSIVE FOR
        SELECT TO authenticated USING (
            EXISTS (
                SELECT 1
                FROM content_documents
                WHERE content_documents.id = content_sections.document_id
                    AND content_documents.user_id = auth.uid()
            )
        );
        RAISE NOTICE 'Created view policy for content_sections';
    ELSE
        RAISE NOTICE 'View policy already exists for content_sections';
    END IF;

    -- Policy for inserting sections
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename = 'content_sections' 
        AND policyname = 'Users can insert sections for their documents'
    ) THEN
        CREATE POLICY "Users can insert sections for their documents" ON "public"."content_sections" AS PERMISSIVE FOR
        INSERT TO authenticated WITH CHECK (
            EXISTS (
                SELECT 1
                FROM content_documents
                WHERE content_documents.id = content_sections.document_id
                    AND content_documents.user_id = auth.uid()
            )
        );
        RAISE NOTICE 'Created insert policy for content_sections';
    ELSE
        RAISE NOTICE 'Insert policy already exists for content_sections';
    END IF;

    -- Policy for updating sections
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename = 'content_sections' 
        AND policyname = 'Users can update sections for their documents'
    ) THEN
        CREATE POLICY "Users can update sections for their documents" ON "public"."content_sections" AS PERMISSIVE FOR
        UPDATE TO authenticated USING (
            EXISTS (
                SELECT 1
                FROM content_documents
                WHERE content_documents.id = content_sections.document_id
                    AND content_documents.user_id = auth.uid()
            )
        ) WITH CHECK (
            EXISTS (
                SELECT 1
                FROM content_documents
                WHERE content_documents.id = content_sections.document_id
                    AND content_documents.user_id = auth.uid()
            )
        );
        RAISE NOTICE 'Created update policy for content_sections';
    ELSE
        RAISE NOTICE 'Update policy already exists for content_sections';
    END IF;

    -- Policy for deleting sections
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename = 'content_sections' 
        AND policyname = 'Users can delete sections for their documents'
    ) THEN
        CREATE POLICY "Users can delete sections for their documents" ON "public"."content_sections" AS PERMISSIVE FOR DELETE TO authenticated USING (
            EXISTS (
                SELECT 1
                FROM content_documents
                WHERE content_documents.id = content_sections.document_id
                    AND content_documents.user_id = auth.uid()
            )
        );
        RAISE NOTICE 'Created delete policy for content_sections';
    ELSE
        RAISE NOTICE 'Delete policy already exists for content_sections';
    END IF;
END $$;

-- Add updated_at trigger for content_sections (safe - only if not exists)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.triggers 
        WHERE event_object_schema = 'public' 
        AND event_object_table = 'content_sections' 
        AND trigger_name = 'update_content_sections_updated_at'
    ) THEN
        CREATE TRIGGER update_content_sections_updated_at BEFORE
        UPDATE ON content_sections FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
        RAISE NOTICE 'Created updated_at trigger for content_sections';
    ELSE
        RAISE NOTICE 'updated_at trigger already exists for content_sections';
    END IF;
END $$;

-- Function to regenerate markdown content from sections (safe - CREATE OR REPLACE)
CREATE OR REPLACE FUNCTION regenerate_markdown_content(doc_id UUID)
RETURNS TEXT
LANGUAGE plpgsql
AS $$
DECLARE
    compiled_markdown TEXT := '';
    section_record RECORD;
BEGIN
    FOR section_record IN
        SELECT section_content, section_title, original_prompt, agent_source, created_at
        FROM content_sections
        WHERE document_id = doc_id
        ORDER BY order_index ASC, created_at ASC
    LOOP
        IF compiled_markdown != '' THEN
            compiled_markdown := compiled_markdown || E'\n\n---\n\n'; -- Separator between sections
        END IF;
        
        compiled_markdown := compiled_markdown || '## ' || section_record.section_title;
        compiled_markdown := compiled_markdown || E'\n\n';

        IF section_record.original_prompt IS NOT NULL THEN
            compiled_markdown := compiled_markdown || '*Prompt: ' || section_record.original_prompt || E'*\n\n';
        END IF;

        compiled_markdown := compiled_markdown || section_record.section_content;
    END LOOP;
    
    RETURN compiled_markdown;
END;
$$;

-- Function to update document markdown content and section count (safe - CREATE OR REPLACE)
CREATE OR REPLACE FUNCTION update_document_markdown_and_count() RETURNS TRIGGER AS $$
DECLARE
    total_sections INTEGER;
    new_markdown_content TEXT;
    doc_id UUID;
BEGIN
    -- Get the document_id (handle both INSERT/UPDATE and DELETE)
    IF TG_OP = 'DELETE' THEN
        doc_id := OLD.document_id;
    ELSE
        doc_id := NEW.document_id;
    END IF;

    -- Count sections and regenerate markdown
    SELECT count(*), regenerate_markdown_content(doc_id)
    INTO total_sections, new_markdown_content
    FROM content_sections
    WHERE document_id = doc_id;

    -- Update the document
    UPDATE content_documents
    SET
        markdown_content = new_markdown_content,
        section_count = total_sections,
        updated_at = NOW()
    WHERE id = doc_id;

    IF TG_OP = 'DELETE' THEN
        RETURN OLD;
    ELSE
        RETURN NEW;
    END IF;
END;
$$ LANGUAGE 'plpgsql';

-- Create triggers to automatically update markdown content when sections change (safe - only if not exists)
DO $$
BEGIN
    -- Insert trigger
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.triggers 
        WHERE event_object_schema = 'public' 
        AND event_object_table = 'content_sections' 
        AND trigger_name = 'update_document_markdown_on_insert'
    ) THEN
        CREATE TRIGGER update_document_markdown_on_insert
        AFTER INSERT ON content_sections
        FOR EACH ROW EXECUTE FUNCTION update_document_markdown_and_count();
        RAISE NOTICE 'Created insert trigger for document markdown updates';
    ELSE
        RAISE NOTICE 'Insert trigger for document markdown updates already exists';
    END IF;

    -- Update trigger
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.triggers 
        WHERE event_object_schema = 'public' 
        AND event_object_table = 'content_sections' 
        AND trigger_name = 'update_document_markdown_on_update'
    ) THEN
        CREATE TRIGGER update_document_markdown_on_update
        AFTER UPDATE ON content_sections
        FOR EACH ROW EXECUTE FUNCTION update_document_markdown_and_count();
        RAISE NOTICE 'Created update trigger for document markdown updates';
    ELSE
        RAISE NOTICE 'Update trigger for document markdown updates already exists';
    END IF;

    -- Delete trigger
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.triggers 
        WHERE event_object_schema = 'public' 
        AND event_object_table = 'content_sections' 
        AND trigger_name = 'update_document_markdown_on_delete'
    ) THEN
        CREATE TRIGGER update_document_markdown_on_delete
        AFTER DELETE ON content_sections
        FOR EACH ROW EXECUTE FUNCTION update_document_markdown_and_count();
        RAISE NOTICE 'Created delete trigger for document markdown updates';
    ELSE
        RAISE NOTICE 'Delete trigger for document markdown updates already exists';
    END IF;
END $$;

-- Commit the transaction
COMMIT;

-- Success message
DO $$
BEGIN
    RAISE NOTICE '✅ Migration completed successfully! Markdown content accumulation feature is now available.';
    RAISE NOTICE 'ℹ️  Existing content_documents remain unchanged and functional.';
    RAISE NOTICE 'ℹ️  New markdown features are ready to use.';
END $$;