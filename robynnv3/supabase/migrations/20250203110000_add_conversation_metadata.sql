-- Add conversation metadata fields to content_documents for enhanced conversation display
-- This migration supports improved conversation tracking and display capabilities

-- Add conversation metadata fields to content_documents table
DO $$ 
BEGIN
    -- Add last_prompt field if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'content_documents' 
        AND column_name = 'last_prompt'
    ) THEN
        ALTER TABLE "public"."content_documents" 
        ADD COLUMN "last_prompt" TEXT;
    END IF;

    -- Add conversation_status field if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'content_documents' 
        AND column_name = 'conversation_status'
    ) THEN
        ALTER TABLE "public"."content_documents" 
        ADD COLUMN "conversation_status" TEXT NOT NULL DEFAULT 'active';
    END IF;

    -- Add tags field if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'content_documents' 
        AND column_name = 'tags'
    ) THEN
        ALTER TABLE "public"."content_documents" 
        ADD COLUMN "tags" TEXT[] DEFAULT '{}';
    END IF;

    -- Add conversation_count field if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'content_documents' 
        AND column_name = 'conversation_count'
    ) THEN
        ALTER TABLE "public"."content_documents" 
        ADD COLUMN "conversation_count" INTEGER NOT NULL DEFAULT 0;
    END IF;

    -- Add last_activity_at field if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'content_documents' 
        AND column_name = 'last_activity_at'
    ) THEN
        ALTER TABLE "public"."content_documents" 
        ADD COLUMN "last_activity_at" TIMESTAMPTZ;
    END IF;
END $$;

-- Create index for faster conversation status queries
CREATE INDEX IF NOT EXISTS idx_content_documents_conversation_status 
ON "public"."content_documents" ("conversation_status");

-- Create index for faster last activity queries
CREATE INDEX IF NOT EXISTS idx_content_documents_last_activity 
ON "public"."content_documents" ("last_activity_at");

-- Create index for faster conversation count queries
CREATE INDEX IF NOT EXISTS idx_content_documents_conversation_count 
ON "public"."content_documents" ("conversation_count");

-- Update existing accumulation documents to have proper initial values
UPDATE "public"."content_documents" 
SET 
    "conversation_count" = COALESCE("section_count", 0),
    "last_activity_at" = COALESCE("updated_at", "created_at"),
    "conversation_status" = CASE 
        WHEN "section_count" > 0 THEN 'active'
        ELSE 'draft'
    END
WHERE "content_mode" = 'accumulation' 
AND ("conversation_count" = 0 OR "last_activity_at" IS NULL);

-- Create or replace function to automatically update conversation metadata
CREATE OR REPLACE FUNCTION update_conversation_metadata()
RETURNS TRIGGER AS $$
BEGIN
    -- Update the parent document's conversation metadata when sections are modified
    IF TG_OP = 'INSERT' THEN
        UPDATE "public"."content_documents"
        SET 
            "conversation_count" = (
                SELECT COUNT(*) 
                FROM "public"."content_sections" 
                WHERE "document_id" = NEW."document_id"
            ),
            "last_activity_at" = NOW(),
            "last_prompt" = COALESCE(NEW."original_prompt", "last_prompt"),
            "conversation_status" = 'active'
        WHERE "id" = NEW."document_id";
        
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE "public"."content_documents"
        SET 
            "conversation_count" = (
                SELECT COUNT(*) 
                FROM "public"."content_sections" 
                WHERE "document_id" = OLD."document_id"
            ),
            "last_activity_at" = NOW()
        WHERE "id" = OLD."document_id";
        
        RETURN OLD;
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically update conversation metadata
DROP TRIGGER IF EXISTS trigger_update_conversation_metadata ON "public"."content_sections";
CREATE TRIGGER trigger_update_conversation_metadata
    AFTER INSERT OR DELETE ON "public"."content_sections"
    FOR EACH ROW
    EXECUTE FUNCTION update_conversation_metadata();

-- Add comment for documentation
COMMENT ON COLUMN "public"."content_documents"."last_prompt" IS 'The most recent research prompt used in this conversation';
COMMENT ON COLUMN "public"."content_documents"."conversation_status" IS 'Status of the conversation: draft, active, paused, completed';
COMMENT ON COLUMN "public"."content_documents"."tags" IS 'Array of tags for categorizing conversations';
COMMENT ON COLUMN "public"."content_documents"."conversation_count" IS 'Number of conversation interactions (sections)';
COMMENT ON COLUMN "public"."content_documents"."last_activity_at" IS 'Timestamp of the last conversation activity';