-- Manual test to see if content_sections insert works
-- Replace 'YOUR_DOCUMENT_ID' and 'YOUR_USER_ID' with real values from get_document_id.sql

-- Test 1: Try to insert a content section manually
INSERT INTO content_sections (
    document_id, 
    user_id, 
    section_title, 
    section_content, 
    agent_source, 
    original_prompt, 
    order_index
) VALUES (
    'YOUR_DOCUMENT_ID',  -- Replace with actual document ID
    'YOUR_USER_ID',      -- Replace with actual user ID  
    'Test Section',
    'This is a test content section to see if inserts work.',
    'manual-test',
    'Test prompt',
    0
);

-- Test 2: Check if the insert worked
SELECT * FROM content_sections 
WHERE document_id = 'YOUR_DOCUMENT_ID'
ORDER BY created_at DESC;

-- Test 3: Test the regenerate function
SELECT regenerate_markdown_content('YOUR_DOCUMENT_ID');